<?php
/**
 * Absence Scheduler
 * Runs automatic absence processing at scheduled intervals during business hours
 */

// Set timezone
date_default_timezone_set('Asia/Manila');

// Include the auto absence manager
require_once 'auto_absence_manager.php';

// Database connection
try {
    $connect = new PDO("mysql:host=localhost;dbname=attendance_monitoring_db", "root", "");
    $connect->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    error_log("Database connection failed: " . $e->getMessage());
    die("Database connection failed");
}

/**
 * Check if scheduler should run
 */
function shouldRunScheduler() {
    $currentHour = (int)date('H');
    $currentMinute = (int)date('i');
    
    // Run between 7 AM and 5 PM
    if ($currentHour < 7 || $currentHour > 17) {
        return false;
    }
    
    // Run every 30 minutes during business hours
    return $currentMinute % 30 === 0;
}

/**
 * Log scheduler activity
 */
function logSchedulerActivity($message, $data = null) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] {$message}";
    
    if ($data) {
        $logMessage .= " | Data: " . json_encode($data);
    }
    
    error_log($logMessage);
    
    // Also log to a specific scheduler log file
    $logFile = __DIR__ . '/logs/absence_scheduler.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logFile, $logMessage . PHP_EOL, FILE_APPEND | LOCK_EX);
}

/**
 * Create or update scheduler status table
 */
function createSchedulerStatusTable($connect) {
    try {
        $sql = "CREATE TABLE IF NOT EXISTS tbl_scheduler_status (
            id INT AUTO_INCREMENT PRIMARY KEY,
            last_run DATETIME NOT NULL,
            next_run DATETIME NOT NULL,
            status ENUM('running', 'completed', 'error') NOT NULL,
            total_processed INT DEFAULT 0,
            marked_absent INT DEFAULT 0,
            errors INT DEFAULT 0,
            details TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $connect->exec($sql);
        return true;
        
    } catch (PDOException $e) {
        logSchedulerActivity("Error creating scheduler status table: " . $e->getMessage());
        return false;
    }
}

/**
 * Update scheduler status
 */
function updateSchedulerStatus($connect, $status, $result = null) {
    try {
        $currentTime = date('Y-m-d H:i:s');
        $nextRun = date('Y-m-d H:i:s', strtotime('+30 minutes'));
        
        $stmt = $connect->prepare("
            INSERT INTO tbl_scheduler_status 
            (last_run, next_run, status, total_processed, marked_absent, errors, details)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        $totalProcessed = $result ? $result['total_employees'] : 0;
        $markedAbsent = $result ? $result['marked_absent'] : 0;
        $errors = $result ? $result['errors'] : 0;
        $details = $result ? json_encode($result) : null;
        
        $stmt->execute([
            $currentTime,
            $nextRun,
            $status,
            $totalProcessed,
            $markedAbsent,
            $errors,
            $details
        ]);
        
        return true;
        
    } catch (PDOException $e) {
        logSchedulerActivity("Error updating scheduler status: " . $e->getMessage());
        return false;
    }
}

/**
 * Get last scheduler run
 */
function getLastSchedulerRun($connect) {
    try {
        $stmt = $connect->prepare("
            SELECT * FROM tbl_scheduler_status 
            ORDER BY created_at DESC 
            LIMIT 1
        ");
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
        
    } catch (PDOException $e) {
        logSchedulerActivity("Error getting last scheduler run: " . $e->getMessage());
        return null;
    }
}

/**
 * Check if we should skip this run (to prevent duplicate processing)
 */
function shouldSkipRun($connect) {
    $lastRun = getLastSchedulerRun($connect);
    
    if (!$lastRun) {
        return false; // No previous run, proceed
    }
    
    $lastRunTime = strtotime($lastRun['last_run']);
    $currentTime = time();
    $timeDiff = $currentTime - $lastRunTime;
    
    // Skip if last run was less than 25 minutes ago (to prevent overlap)
    return $timeDiff < (25 * 60);
}

/**
 * Main scheduler function
 */
function runScheduler($connect) {
    $startTime = microtime(true);
    
    logSchedulerActivity("Starting automatic absence scheduler");
    
    // Create scheduler status table if it doesn't exist
    createSchedulerStatusTable($connect);
    
    // Check if we should skip this run
    if (shouldSkipRun($connect)) {
        logSchedulerActivity("Skipping run - too soon since last execution");
        return;
    }
    
    // Update status to running
    updateSchedulerStatus($connect, 'running');
    
    try {
        // Run the automatic absence processing
        $result = processAutomaticAbsences($connect);
        
        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 2);
        
        // Add execution time to result
        $result['execution_time'] = $executionTime;
        $result['scheduler_run'] = true;
        
        // Update status to completed
        updateSchedulerStatus($connect, 'completed', $result);
        
        // Log summary
        $summary = sprintf(
            "Scheduler completed in %s seconds. Processed: %d, Marked Absent: %d, Errors: %d",
            $executionTime,
            $result['total_employees'],
            $result['marked_absent'],
            $result['errors']
        );
        
        logSchedulerActivity($summary, $result);
        
        return $result;
        
    } catch (Exception $e) {
        $errorResult = [
            'error' => true,
            'message' => $e->getMessage(),
            'execution_time' => round(microtime(true) - $startTime, 2)
        ];
        
        // Update status to error
        updateSchedulerStatus($connect, 'error', $errorResult);
        
        logSchedulerActivity("Scheduler error: " . $e->getMessage(), $errorResult);
        
        return $errorResult;
    }
}

// Handle different execution modes
$mode = $_GET['mode'] ?? 'check';

switch ($mode) {
    case 'run':
        // Force run the scheduler
        header('Content-Type: application/json');
        $result = runScheduler($connect);
        echo json_encode($result, JSON_PRETTY_PRINT);
        break;
        
    case 'status':
        // Get scheduler status
        header('Content-Type: application/json');
        $lastRun = getLastSchedulerRun($connect);
        $currentTime = date('Y-m-d H:i:s');
        $shouldRun = shouldRunScheduler();
        
        echo json_encode([
            'current_time' => $currentTime,
            'should_run_now' => $shouldRun,
            'last_run' => $lastRun,
            'business_hours' => [
                'start' => '07:00',
                'end' => '17:00',
                'interval' => '30 minutes'
            ]
        ], JSON_PRETTY_PRINT);
        break;
        
    case 'check':
    default:
        // Check if scheduler should run and run if needed
        if (shouldRunScheduler()) {
            $result = runScheduler($connect);
            
            if (php_sapi_name() === 'cli') {
                // Command line output
                echo "Scheduler executed at " . date('Y-m-d H:i:s') . "\n";
                echo "Result: " . json_encode($result, JSON_PRETTY_PRINT) . "\n";
            } else {
                // Web output
                header('Content-Type: application/json');
                echo json_encode($result, JSON_PRETTY_PRINT);
            }
        } else {
            $message = "Scheduler not scheduled to run at this time";
            logSchedulerActivity($message);
            
            if (php_sapi_name() === 'cli') {
                echo $message . "\n";
            } else {
                header('Content-Type: application/json');
                echo json_encode([
                    'message' => $message,
                    'current_time' => date('Y-m-d H:i:s'),
                    'should_run' => false
                ]);
            }
        }
        break;
}
?>
