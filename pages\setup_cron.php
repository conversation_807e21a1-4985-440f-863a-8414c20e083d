<?php
/**
 * Cron Job Setup for Automatic Absence Processing
 * This file helps set up automatic scheduling for absence processing
 */

session_start();
date_default_timezone_set('Asia/Manila');

// Check if user is logged in and is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'Admin') {
    header('Location: login.php');
    exit();
}

$setupComplete = false;
$cronCommand = '';
$windowsTaskCommand = '';

// Generate cron commands
$phpPath = PHP_BINARY;
$scriptPath = __DIR__ . '/absence_scheduler.php';
$logPath = __DIR__ . '/logs/cron_absence.log';

// Linux/Unix cron command (runs every 30 minutes between 7 AM and 5 PM)
$cronCommand = "*/30 7-17 * * 1-5 {$phpPath} {$scriptPath} >> {$logPath} 2>&1";

// Windows Task Scheduler command
$windowsTaskCommand = "schtasks /create /tn \"OJT_Absence_Scheduler\" /tr \"{$phpPath} {$scriptPath}\" /sc minute /mo 30 /st 07:00 /et 17:00 /f";

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cron Job Setup - OJT Monitoring System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <style>
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            overflow-x: auto;
        }
        .step-card {
            border-left: 4px solid #007bff;
            margin-bottom: 1rem;
        }
        .warning-card {
            border-left: 4px solid #ffc107;
        }
        .success-card {
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0"><i class="fas fa-clock me-2"></i>Automatic Absence Scheduler Setup</h1>
                        <p class="text-muted mb-0">Configure automatic absence processing to run during business hours</p>
                    </div>
                    <a href="absence_dashboard.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>
        </div>

        <!-- System Information -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-info-circle me-2"></i>System Information</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Operating System:</strong></td>
                                <td><?php echo PHP_OS; ?></td>
                            </tr>
                            <tr>
                                <td><strong>PHP Path:</strong></td>
                                <td><code><?php echo $phpPath; ?></code></td>
                            </tr>
                            <tr>
                                <td><strong>Script Path:</strong></td>
                                <td><code><?php echo $scriptPath; ?></code></td>
                            </tr>
                            <tr>
                                <td><strong>Log Path:</strong></td>
                                <td><code><?php echo $logPath; ?></code></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-calendar-alt me-2"></i>Schedule Configuration</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Frequency:</strong></td>
                                <td>Every 30 minutes</td>
                            </tr>
                            <tr>
                                <td><strong>Business Hours:</strong></td>
                                <td>7:00 AM - 5:00 PM</td>
                            </tr>
                            <tr>
                                <td><strong>Working Days:</strong></td>
                                <td>Monday - Friday</td>
                            </tr>
                            <tr>
                                <td><strong>Timezone:</strong></td>
                                <td>Asia/Manila</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Setup Instructions -->
        <?php if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN'): ?>
            <!-- Windows Instructions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card step-card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fab fa-windows me-2"></i>Windows Task Scheduler Setup</h5>
                            
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Note:</strong> You need administrator privileges to create scheduled tasks.
                            </div>

                            <h6>Method 1: Automatic Setup (Recommended)</h6>
                            <p>Click the button below to automatically create the scheduled task:</p>
                            <button class="btn btn-primary mb-3" onclick="createWindowsTask()">
                                <i class="fas fa-magic"></i> Create Windows Task
                            </button>

                            <h6>Method 2: Manual Setup</h6>
                            <ol>
                                <li>Open Command Prompt as Administrator</li>
                                <li>Copy and paste the following command:</li>
                            </ol>
                            <div class="code-block">
                                <?php echo htmlspecialchars($windowsTaskCommand); ?>
                            </div>
                            <button class="btn btn-outline-secondary btn-sm mt-2" onclick="copyToClipboard('windowsCommand')">
                                <i class="fas fa-copy"></i> Copy Command
                            </button>

                            <h6 class="mt-3">Method 3: Task Scheduler GUI</h6>
                            <ol>
                                <li>Open Task Scheduler (taskschd.msc)</li>
                                <li>Click "Create Basic Task"</li>
                                <li>Name: "OJT Absence Scheduler"</li>
                                <li>Trigger: Daily</li>
                                <li>Start time: 07:00 AM</li>
                                <li>Repeat every: 30 minutes</li>
                                <li>Duration: 10 hours (until 5 PM)</li>
                                <li>Action: Start a program</li>
                                <li>Program: <code><?php echo $phpPath; ?></code></li>
                                <li>Arguments: <code><?php echo $scriptPath; ?></code></li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- Linux/Unix Instructions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card step-card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fab fa-linux me-2"></i>Linux/Unix Cron Job Setup</h5>
                            
                            <h6>Step 1: Open Crontab</h6>
                            <p>Open your terminal and run:</p>
                            <div class="code-block">
                                crontab -e
                            </div>

                            <h6>Step 2: Add Cron Job</h6>
                            <p>Add the following line to your crontab:</p>
                            <div class="code-block" id="cronCommand">
                                <?php echo htmlspecialchars($cronCommand); ?>
                            </div>
                            <button class="btn btn-outline-secondary btn-sm mt-2" onclick="copyToClipboard('cronCommand')">
                                <i class="fas fa-copy"></i> Copy Command
                            </button>

                            <h6 class="mt-3">Step 3: Save and Exit</h6>
                            <p>Save the file and exit the editor. The cron job will be automatically activated.</p>

                            <h6>Verify Cron Job</h6>
                            <p>To verify the cron job was added successfully:</p>
                            <div class="code-block">
                                crontab -l
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Alternative: Web-based Scheduler -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card warning-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-globe me-2"></i>Alternative: Web-based Scheduler</h5>
                        <p>If you cannot set up system-level scheduling, you can use a web-based approach:</p>
                        
                        <h6>Option 1: External Cron Service</h6>
                        <p>Use services like <strong>cron-job.org</strong> or <strong>easycron.com</strong> to call:</p>
                        <div class="code-block">
                            <?php echo (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/absence_scheduler.php?mode=check'; ?>
                        </div>
                        <button class="btn btn-outline-secondary btn-sm mt-2" onclick="copyToClipboard('webUrl')">
                            <i class="fas fa-copy"></i> Copy URL
                        </button>

                        <h6 class="mt-3">Option 2: Manual Execution</h6>
                        <p>Manually run the scheduler from the absence dashboard when needed.</p>
                        <a href="absence_dashboard.php" class="btn btn-outline-primary">
                            <i class="fas fa-external-link-alt"></i> Go to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Testing -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card success-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-vial me-2"></i>Test the Scheduler</h5>
                        <p>Test if the automatic absence scheduler is working correctly:</p>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <button class="btn btn-success w-100 mb-2" onclick="testScheduler()">
                                    <i class="fas fa-play"></i> Test Run Scheduler
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-info w-100 mb-2" onclick="checkStatus()">
                                    <i class="fas fa-info"></i> Check Status
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-secondary w-100 mb-2" onclick="viewLogs()">
                                    <i class="fas fa-file-alt"></i> View Logs
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Important Notes -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-exclamation-triangle me-2"></i>Important Notes</h5>
                        <ul>
                            <li><strong>Permissions:</strong> Ensure the web server has write permissions to the logs directory</li>
                            <li><strong>PHP Path:</strong> Verify the PHP path is correct for your system</li>
                            <li><strong>Database Access:</strong> The scheduler needs database access with the same credentials</li>
                            <li><strong>Timezone:</strong> Ensure your server timezone is set to Asia/Manila</li>
                            <li><strong>Holidays:</strong> The scheduler automatically skips holidays defined in tbl_holidays</li>
                            <li><strong>Business Hours:</strong> Only runs between 7 AM and 5 PM on weekdays</li>
                            <li><strong>Monitoring:</strong> Check the absence dashboard regularly to monitor scheduler activity</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden elements for copying -->
    <textarea id="windowsCommand" style="position: absolute; left: -9999px;"><?php echo $windowsTaskCommand; ?></textarea>
    <textarea id="webUrl" style="position: absolute; left: -9999px;"><?php echo (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/absence_scheduler.php?mode=check'; ?></textarea>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = elementId === 'cronCommand' ? element.textContent : element.value;
            
            navigator.clipboard.writeText(text).then(() => {
                Swal.fire({
                    icon: 'success',
                    title: 'Copied!',
                    text: 'Command copied to clipboard',
                    timer: 2000,
                    showConfirmButton: false
                });
            }).catch(() => {
                // Fallback for older browsers
                element.select();
                document.execCommand('copy');
                Swal.fire({
                    icon: 'success',
                    title: 'Copied!',
                    text: 'Command copied to clipboard',
                    timer: 2000,
                    showConfirmButton: false
                });
            });
        }

        async function createWindowsTask() {
            const result = await Swal.fire({
                title: 'Create Windows Task?',
                text: 'This will attempt to create a scheduled task. You may need administrator privileges.',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'Yes, create it!',
                cancelButtonText: 'Cancel'
            });

            if (result.isConfirmed) {
                Swal.fire({
                    icon: 'info',
                    title: 'Creating Task...',
                    text: 'Please run the command in an administrator command prompt.',
                    footer: 'Copy the command from the manual setup section above.'
                });
            }
        }

        async function testScheduler() {
            try {
                Swal.fire({
                    title: 'Testing Scheduler...',
                    text: 'Running test execution',
                    allowOutsideClick: false,
                    didOpen: () => Swal.showLoading()
                });

                const response = await fetch('absence_scheduler.php?mode=run');
                const data = await response.json();

                Swal.fire({
                    icon: data.error ? 'error' : 'success',
                    title: data.error ? 'Test Failed' : 'Test Successful',
                    html: `
                        <div class="text-left">
                            ${data.error ? `
                                <p><strong>Error:</strong> ${data.message}</p>
                            ` : `
                                <p><strong>Execution Time:</strong> ${data.execution_time} seconds</p>
                                <p><strong>Total Employees:</strong> ${data.total_employees}</p>
                                <p><strong>Marked Absent:</strong> ${data.marked_absent}</p>
                                <p><strong>Errors:</strong> ${data.errors}</p>
                            `}
                        </div>
                    `
                });
            } catch (error) {
                Swal.fire('Error', 'Failed to test scheduler: ' + error.message, 'error');
            }
        }

        async function checkStatus() {
            try {
                const response = await fetch('absence_scheduler.php?mode=status');
                const data = await response.json();

                Swal.fire({
                    icon: 'info',
                    title: 'Scheduler Status',
                    html: `
                        <div class="text-left">
                            <p><strong>Current Time:</strong> ${data.current_time}</p>
                            <p><strong>Should Run Now:</strong> ${data.should_run_now ? 'Yes' : 'No'}</p>
                            <p><strong>Business Hours:</strong> ${data.business_hours.start} - ${data.business_hours.end}</p>
                            ${data.last_run ? `
                                <hr>
                                <p><strong>Last Run:</strong> ${data.last_run.last_run}</p>
                                <p><strong>Status:</strong> ${data.last_run.status}</p>
                                <p><strong>Processed:</strong> ${data.last_run.total_processed} employees</p>
                            ` : '<p><em>No previous runs found</em></p>'}
                        </div>
                    `,
                    width: 600
                });
            } catch (error) {
                Swal.fire('Error', 'Failed to get status', 'error');
            }
        }

        function viewLogs() {
            Swal.fire({
                icon: 'info',
                title: 'View Logs',
                html: `
                    <p>Log files are stored at:</p>
                    <code><?php echo $logPath; ?></code>
                    <p class="mt-3">You can also check the absence dashboard for recent activity.</p>
                `,
                footer: '<a href="absence_dashboard.php">Go to Absence Dashboard</a>'
            });
        }
    </script>
</body>
</html>
