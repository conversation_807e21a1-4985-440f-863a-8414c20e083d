<?php
/**
 * Simple Absence Checker
 * Checks employees and marks them absent if no attendance after 5 PM
 */

header('Content-Type: application/json');
date_default_timezone_set('Asia/Manila');

// Database connection
try {
    $connect = new PDO("mysql:host=localhost;dbname=attendance_monitoring_db", "root", "");
    $connect->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => true, 'message' => 'Database connection failed']);
    exit;
}

$currentDate = date('Y-m-d');
$currentTime = date('H:i:s');
$currentHour = (int)date('H');

// Check if it's past 5 PM
$isPast5PM = $currentTime >= '17:00:00';

// Get all employees
try {
    $stmt = $connect->prepare("
        SELECT tbl_emp_id, employee_id, first_name, middle_initial, surname, no_hours_required
        FROM tbl_employee 
        WHERE user_role = 'Employee'
    ");
    $stmt->execute();
    $employees = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $result = [
        'current_time' => date('Y-m-d H:i:s'),
        'is_past_5pm' => $isPast5PM,
        'total_employees' => count($employees),
        'employees_checked' => 0,
        'already_completed' => 0,
        'already_recorded' => 0,
        'marked_absent' => 0,
        'marked_absent_list' => []
    ];
    
    foreach ($employees as $employee) {
        $result['employees_checked']++;
        
        $tblEmpId = $employee['tbl_emp_id'];
        $employeeName = trim($employee['first_name'] . ' ' . 
                            ($employee['middle_initial'] ? $employee['middle_initial'] . ' ' : '') . 
                            $employee['surname']);
        $requiredHours = $employee['no_hours_required'] ?? 0;
        
        // Check if employee has completed required hours
        $hoursStmt = $connect->prepare("
            SELECT SUM(total_daily_hours) as total_hours 
            FROM tbl_attendance 
            WHERE tbl_emp_id = ? AND is_absent = 0
        ");
        $hoursStmt->execute([$tblEmpId]);
        $hoursResult = $hoursStmt->fetch(PDO::FETCH_ASSOC);
        $totalHours = $hoursResult['total_hours'] ?? 0;
        
        // Skip if hours completed
        if ($totalHours >= $requiredHours) {
            $result['already_completed']++;
            continue;
        }
        
        // Check if employee already has attendance record for today
        $attendanceStmt = $connect->prepare("
            SELECT tbl_attendance_id 
            FROM tbl_attendance 
            WHERE tbl_emp_id = ? AND DATE(attendance_date) = ?
        ");
        $attendanceStmt->execute([$tblEmpId, $currentDate]);
        $attendanceRecord = $attendanceStmt->fetch(PDO::FETCH_ASSOC);
        
        // Skip if already has record
        if ($attendanceRecord) {
            $result['already_recorded']++;
            continue;
        }
        
        // Mark as absent if past 5 PM and no record
        if ($isPast5PM) {
            try {
                $insertStmt = $connect->prepare("
                    INSERT INTO tbl_attendance 
                    (attendance_date, morning_time_in, morning_time_out, afternoon_time_in, afternoon_time_out,
                     morning_hours, afternoon_hours, total_daily_hours, is_absent, allow_overtime, tbl_emp_id)
                    VALUES (?, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, ?)
                ");
                $insertStmt->execute([$currentDate, $tblEmpId]);
                
                $result['marked_absent']++;
                $result['marked_absent_list'][] = [
                    'employee_id' => $employee['employee_id'],
                    'employee_name' => $employeeName
                ];
                
            } catch (PDOException $e) {
                // Skip if already exists (duplicate key error)
                if ($e->getCode() != 23000) {
                    error_log("Error marking employee absent: " . $e->getMessage());
                }
            }
        }
    }
    
    echo json_encode($result);
    
} catch (PDOException $e) {
    echo json_encode(['error' => true, 'message' => 'Database error: ' . $e->getMessage()]);
}
?>
