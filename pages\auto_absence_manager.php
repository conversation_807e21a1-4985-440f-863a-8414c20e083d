<?php
/**
 * Automatic Absence Manager
 * Handles automatic marking of employees as absent based on time ranges and attendance records
 */

// Set timezone
date_default_timezone_set('Asia/Manila');

// Database connection
try {
    $connect = new PDO("mysql:host=localhost;dbname=attendance_monitoring_db", "root", "");
    $connect->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    error_log("Database connection failed: " . $e->getMessage());
    die("Database connection failed");
}

/**
 * Main function to process automatic absences
 */
function processAutomaticAbsences($connect) {
    $result = [
        'total_employees' => 0,
        'already_completed' => 0,
        'already_recorded' => 0,
        'marked_absent' => 0,
        'errors' => 0,
        'processed_employees' => [],
        'error_details' => []
    ];

    $currentDate = date('Y-m-d');
    $currentTime = date('H:i:s');
    $currentHour = (int)date('H');

    // Only process during business hours (7 AM to 5 PM)
    if ($currentHour < 7 || $currentHour > 17) {
        $result['message'] = 'Automatic absence processing only runs between 7 AM and 5 PM';
        return $result;
    }

    // Check if today is a holiday
    if (isHoliday($connect, $currentDate)) {
        $result['message'] = 'Today is a holiday - no absence processing needed';
        return $result;
    }

    try {
        // Get all active employees
        $employeeStmt = $connect->prepare("
            SELECT tbl_emp_id, employee_id, first_name, middle_initial, surname,
                   no_hours_required
            FROM tbl_employee
            WHERE user_role = 'Employee'
            ORDER BY employee_id
        ");
        $employeeStmt->execute();
        $employees = $employeeStmt->fetchAll(PDO::FETCH_ASSOC);

        $result['total_employees'] = count($employees);

        foreach ($employees as $employee) {
            $processResult = processEmployeeAbsence($connect, $employee, $currentDate, $currentTime);

            // Update counters
            switch ($processResult['status']) {
                case 'completed':
                    $result['already_completed']++;
                    break;
                case 'recorded':
                    $result['already_recorded']++;
                    break;
                case 'marked_absent':
                    $result['marked_absent']++;
                    break;
                case 'error':
                    $result['errors']++;
                    $result['error_details'][] = $processResult['message'];
                    break;
            }

            $result['processed_employees'][] = $processResult;
        }

    } catch (PDOException $e) {
        error_log("Error in automatic absence processing: " . $e->getMessage());
        $result['errors']++;
        $result['error_details'][] = $e->getMessage();
    }

    return $result;
}

/**
 * Process individual employee for absence
 */
function processEmployeeAbsence($connect, $employee, $currentDate, $currentTime) {
    $tblEmpId = $employee['tbl_emp_id'];
    $employeeName = trim($employee['first_name'] . ' ' .
                        ($employee['middle_initial'] ? $employee['middle_initial'] . ' ' : '') .
                        $employee['surname']);

    $result = [
        'employee_id' => $employee['employee_id'],
        'employee_name' => $employeeName,
        'tbl_emp_id' => $tblEmpId,
        'status' => '',
        'message' => '',
        'timestamp' => date('Y-m-d H:i:s')
    ];

    try {
        // Check if employee has completed required hours
        if (hasCompletedRequiredHours($connect, $tblEmpId, $employee['no_hours_required'])) {
            $result['status'] = 'completed';
            $result['message'] = 'Employee has completed required hours';
            return $result;
        }

        // Check if employee already has attendance record for today
        $attendanceStmt = $connect->prepare("
            SELECT tbl_attendance_id, is_absent, morning_time_in, afternoon_time_in
            FROM tbl_attendance
            WHERE tbl_emp_id = ? AND DATE(attendance_date) = ?
        ");
        $attendanceStmt->execute([$tblEmpId, $currentDate]);
        $attendanceRecord = $attendanceStmt->fetch(PDO::FETCH_ASSOC);

        if ($attendanceRecord) {
            $result['status'] = 'recorded';
            $result['message'] = 'Employee already has attendance record for today';
            return $result;
        }

        // Check if we should mark as absent based on time ranges
        if (shouldMarkAsAbsent($connect, $currentTime)) {
            $markResult = markEmployeeAsAbsent($connect, $tblEmpId, $currentDate);

            if ($markResult['success']) {
                $result['status'] = 'marked_absent';
                $result['message'] = 'Employee automatically marked as absent';
            } else {
                $result['status'] = 'error';
                $result['message'] = 'Failed to mark employee as absent: ' . $markResult['error'];
            }
        } else {
            $result['status'] = 'waiting';
            $result['message'] = 'Still within attendance time range';
        }

    } catch (Exception $e) {
        $result['status'] = 'error';
        $result['message'] = 'Error processing employee: ' . $e->getMessage();
        error_log("Error processing employee {$employeeName} (ID: {$tblEmpId}): " . $e->getMessage());
    }

    return $result;
}

/**
 * Check if employee has completed required hours
 */
function hasCompletedRequiredHours($connect, $tblEmpId, $requiredHours) {
    try {
        $stmt = $connect->prepare("
            SELECT SUM(total_daily_hours) as total_hours
            FROM tbl_attendance
            WHERE tbl_emp_id = ? AND is_absent = 0
        ");
        $stmt->execute([$tblEmpId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        $totalHours = $result['total_hours'] ?? 0;
        return $totalHours >= $requiredHours;

    } catch (PDOException $e) {
        error_log("Error checking completed hours for employee ID {$tblEmpId}: " . $e->getMessage());
        return false;
    }
}

/**
 * Check if current time is past the attendance window
 */
function shouldMarkAsAbsent($connect, $currentTime) {
    try {
        // Get active time range
        $stmt = $connect->prepare("
            SELECT morning_time_in_end, afternoon_time_in_end
            FROM tbl_time_ranges
            WHERE activate_this_timerange = 1
            LIMIT 1
        ");
        $stmt->execute();
        $timeRange = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$timeRange) {
            // Default time ranges if none found
            $morningCutoff = '08:30:00';
            $afternoonCutoff = '13:30:00';
        } else {
            $morningCutoff = date('H:i:s', strtotime($timeRange['morning_time_in_end']));
            $afternoonCutoff = date('H:i:s', strtotime($timeRange['afternoon_time_in_end']));
        }

        // Add 30-minute grace period
        $morningCutoffWithGrace = date('H:i:s', strtotime($morningCutoff . ' +30 minutes'));
        $afternoonCutoffWithGrace = date('H:i:s', strtotime($afternoonCutoff . ' +30 minutes'));

        // Check if current time is past both morning and afternoon cutoffs
        return $currentTime > $afternoonCutoffWithGrace;

    } catch (PDOException $e) {
        error_log("Error checking time ranges: " . $e->getMessage());
        // Default behavior: mark as absent after 2 PM (13:30 + 30 min grace)
        return $currentTime > '14:00:00';
    }
}

/**
 * Mark employee as absent
 */
function markEmployeeAsAbsent($connect, $tblEmpId, $currentDate) {
    try {
        $connect->beginTransaction();

        $stmt = $connect->prepare("
            INSERT INTO tbl_attendance
            (attendance_date, morning_time_in, morning_time_out, afternoon_time_in, afternoon_time_out,
             morning_hours, afternoon_hours, total_daily_hours, is_absent, allow_overtime, tbl_emp_id)
            VALUES (?, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, ?)
        ");

        $stmt->execute([$currentDate, $tblEmpId]);

        $connect->commit();

        return ['success' => true];

    } catch (PDOException $e) {
        $connect->rollBack();
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Check if date is a holiday
 */
function isHoliday($connect, $date) {
    try {
        $stmt = $connect->prepare("
            SELECT holiday_name
            FROM tbl_holidays
            WHERE holiday_date = ?
               OR (is_recurring = 1 AND MONTH(holiday_date) = MONTH(?) AND DAY(holiday_date) = DAY(?))
        ");
        $stmt->execute([$date, $date, $date]);
        return $stmt->fetch(PDO::FETCH_ASSOC) !== false;

    } catch (PDOException $e) {
        error_log("Error checking holiday: " . $e->getMessage());
        return false;
    }
}

// If called directly, process absences and return JSON
if (basename($_SERVER['PHP_SELF']) === 'auto_absence_manager.php') {
    header('Content-Type: application/json');

    $result = processAutomaticAbsences($connect);
    echo json_encode($result, JSON_PRETTY_PRINT);
}
?>
