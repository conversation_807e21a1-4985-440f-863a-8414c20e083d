<?php
/**
 * Absence Management Dashboard
 * Displays employee status and automatic absence processing information
 */

session_start();
date_default_timezone_set('Asia/Manila');

// Check if user is logged in and is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'Admin') {
    header('Location: login.php');
    exit();
}

// Database connection
try {
    $connect = new PDO("mysql:host=localhost;dbname=attendance_monitoring_db", "root", "");
    $connect->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

$currentDate = date('Y-m-d');
$currentTime = date('H:i:s');
$currentHour = (int)date('H');
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Absence Management Dashboard - OJT Monitoring System</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <style>
        .status-card {
            transition: transform 0.2s;
        }
        .status-card:hover {
            transform: translateY(-2px);
        }
        .employee-card {
            border-left: 4px solid #dee2e6;
        }
        .employee-card.completed {
            border-left-color: #28a745;
        }
        .employee-card.absent {
            border-left-color: #dc3545;
        }
        .employee-card.present {
            border-left-color: #007bff;
        }
        .employee-card.in-progress {
            border-left-color: #17a2b8;
        }
        .scheduler-status {
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .scheduler-status.active {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }
        .scheduler-status.inactive {
            background: linear-gradient(135deg, #6c757d, #adb5bd);
            color: white;
        }
        .progress-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0"><i class="fas fa-calendar-check me-2"></i>Absence Management Dashboard</h1>
                        <p class="text-muted mb-0">Monitor automatic absence processing and employee status</p>
                    </div>
                    <div class="text-end">
                        <div class="badge bg-primary fs-6"><?php echo date('F j, Y'); ?></div>
                        <div class="badge bg-secondary fs-6" id="currentTime"><?php echo date('h:i:s A'); ?></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scheduler Status -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="scheduler-status <?php echo ($currentHour >= 7 && $currentHour <= 17) ? 'active' : 'inactive'; ?>" id="schedulerStatus">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-1">
                                <i class="fas fa-robot me-2"></i>Automatic Absence Scheduler
                            </h5>
                            <p class="mb-0" id="schedulerStatusText">
                                <?php if ($currentHour >= 7 && $currentHour <= 17): ?>
                                    Active - Running every 30 minutes during business hours (7 AM - 5 PM)
                                <?php else: ?>
                                    Inactive - Outside business hours
                                <?php endif; ?>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <button class="btn btn-light btn-sm me-2" onclick="checkSchedulerStatus()">
                                <i class="fas fa-sync-alt"></i> Check Status
                            </button>
                            <button class="btn btn-warning btn-sm" onclick="runSchedulerNow()">
                                <i class="fas fa-play"></i> Run Now
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4" id="statisticsCards">
            <!-- Statistics will be loaded here -->
        </div>

        <!-- Controls -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-filter me-2"></i>Filters</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <select class="form-select form-select-sm" id="statusFilter">
                                    <option value="">All Status</option>
                                    <option value="completed">Hours Completed</option>
                                    <option value="absent">Absent Today</option>
                                    <option value="present">Present Today</option>
                                    <option value="no_record">No Record Today</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <input type="text" class="form-control form-control-sm" id="searchEmployee" placeholder="Search employee...">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-tools me-2"></i>Actions</h6>
                        <button class="btn btn-primary btn-sm me-2" onclick="refreshData()">
                            <i class="fas fa-refresh"></i> Refresh Data
                        </button>
                        <button class="btn btn-success btn-sm me-2" onclick="exportData()">
                            <i class="fas fa-download"></i> Export
                        </button>
                        <button class="btn btn-info btn-sm" onclick="viewLogs()">
                            <i class="fas fa-file-alt"></i> View Logs
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Employee List -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-users me-2"></i>Employee Status</h6>
                    </div>
                    <div class="card-body">
                        <div id="loadingSpinner" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2 text-muted">Loading employee data...</p>
                        </div>
                        <div id="employeeList" style="display: none;">
                            <!-- Employee cards will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        let employeesData = [];
        let filteredData = [];

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadEmployeeData();
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);

            // Set up event listeners
            document.getElementById('statusFilter').addEventListener('change', filterEmployees);
            document.getElementById('searchEmployee').addEventListener('input', filterEmployees);
        });

        // Update current time
        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', {
                hour12: true,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }

        // Load employee data
        async function loadEmployeeData() {
            try {
                const response = await fetch('get_employees_status.php?action=all');
                const data = await response.json();

                if (data.success) {
                    employeesData = data.employees;
                    filteredData = [...employeesData];

                    updateStatistics(data);
                    renderEmployeeList();

                    document.getElementById('loadingSpinner').style.display = 'none';
                    document.getElementById('employeeList').style.display = 'block';
                } else {
                    throw new Error(data.error || 'Failed to load employee data');
                }
            } catch (error) {
                console.error('Error loading employee data:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to load employee data: ' + error.message
                });
            }
        }

        // Update statistics cards
        function updateStatistics(data) {
            const stats = {
                total: data.employees.length,
                completed: data.employees.filter(e => e.is_completed).length,
                absent: data.employees.filter(e => e.today_attendance.is_absent).length,
                present: data.employees.filter(e => e.today_attendance.has_record && !e.today_attendance.is_absent).length,
                noRecord: data.employees.filter(e => !e.today_attendance.has_record).length
            };

            const statisticsHTML = `
                <div class="col-md-2">
                    <div class="card status-card text-center">
                        <div class="card-body">
                            <div class="progress-circle bg-primary mx-auto mb-2">${stats.total}</div>
                            <h6 class="card-title mb-0">Total Employees</h6>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card status-card text-center">
                        <div class="card-body">
                            <div class="progress-circle bg-success mx-auto mb-2">${stats.completed}</div>
                            <h6 class="card-title mb-0">Hours Completed</h6>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card status-card text-center">
                        <div class="card-body">
                            <div class="progress-circle bg-info mx-auto mb-2">${stats.present}</div>
                            <h6 class="card-title mb-0">Present Today</h6>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card status-card text-center">
                        <div class="card-body">
                            <div class="progress-circle bg-danger mx-auto mb-2">${stats.absent}</div>
                            <h6 class="card-title mb-0">Absent Today</h6>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card status-card text-center">
                        <div class="card-body">
                            <div class="progress-circle bg-warning mx-auto mb-2">${stats.noRecord}</div>
                            <h6 class="card-title mb-0">No Record</h6>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card status-card text-center">
                        <div class="card-body">
                            <div class="progress-circle bg-secondary mx-auto mb-2">${Math.round((stats.present / stats.total) * 100)}%</div>
                            <h6 class="card-title mb-0">Attendance Rate</h6>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('statisticsCards').innerHTML = statisticsHTML;
        }

        // Render employee list
        function renderEmployeeList() {
            const container = document.getElementById('employeeList');

            if (filteredData.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No employees found</h5>
                        <p class="text-muted">Try adjusting your filters</p>
                    </div>
                `;
                return;
            }

            const employeeCards = filteredData.map(employee => {
                const statusClass = getStatusClass(employee);
                const statusBadge = getStatusBadge(employee);
                const progressBar = getProgressBar(employee);

                return `
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card employee-card ${statusClass}">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="card-title mb-1">${employee.employee_name}</h6>
                                        <small class="text-muted">${employee.employee_id} | ${employee.course}</small>
                                    </div>
                                    ${statusBadge}
                                </div>

                                <div class="mb-2">
                                    <small class="text-muted">Hours Progress</small>
                                    ${progressBar}
                                    <div class="d-flex justify-content-between">
                                        <small>${employee.total_hours}h completed</small>
                                        <small>${employee.required_hours}h required</small>
                                    </div>
                                </div>

                                <div class="row text-center">
                                    <div class="col-4">
                                        <small class="text-muted d-block">Present</small>
                                        <strong class="text-success">${employee.total_days_present}</strong>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-muted d-block">Absent</small>
                                        <strong class="text-danger">${employee.total_days_absent}</strong>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-muted d-block">Remaining</small>
                                        <strong class="text-primary">${employee.remaining_hours}h</strong>
                                    </div>
                                </div>

                                <div class="mt-2">
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewEmployeeDetails('${employee.tbl_emp_id}')">
                                        <i class="fas fa-eye"></i> Details
                                    </button>
                                    ${!employee.is_completed ? `
                                        <button class="btn btn-sm btn-outline-warning" onclick="markAsAbsent('${employee.tbl_emp_id}')">
                                            <i class="fas fa-user-times"></i> Mark Absent
                                        </button>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            container.innerHTML = `<div class="row">${employeeCards}</div>`;
        }

        // Get status class for employee card
        function getStatusClass(employee) {
            if (employee.is_completed) return 'completed';
            if (employee.today_attendance.is_absent) return 'absent';
            if (employee.today_attendance.has_record) return 'present';
            return '';
        }

        // Get status badge
        function getStatusBadge(employee) {
            const status = employee.current_status;
            return `<span class="badge bg-${status.color}">${status.label}</span>`;
        }

        // Get progress bar
        function getProgressBar(employee) {
            const percentage = Math.min(employee.completion_percentage, 100);
            const colorClass = percentage >= 100 ? 'bg-success' : percentage >= 75 ? 'bg-info' : percentage >= 50 ? 'bg-warning' : 'bg-danger';

            return `
                <div class="progress mb-1" style="height: 6px;">
                    <div class="progress-bar ${colorClass}" style="width: ${percentage}%"></div>
                </div>
            `;
        }

        // Filter employees
        function filterEmployees() {
            const statusFilter = document.getElementById('statusFilter').value;
            const searchTerm = document.getElementById('searchEmployee').value.toLowerCase();

            filteredData = employeesData.filter(employee => {
                // Status filter
                let statusMatch = true;
                if (statusFilter) {
                    switch (statusFilter) {
                        case 'completed':
                            statusMatch = employee.is_completed;
                            break;
                        case 'absent':
                            statusMatch = employee.today_attendance.is_absent;
                            break;
                        case 'present':
                            statusMatch = employee.today_attendance.has_record && !employee.today_attendance.is_absent;
                            break;
                        case 'no_record':
                            statusMatch = !employee.today_attendance.has_record;
                            break;
                    }
                }

                // Search filter
                const searchMatch = !searchTerm ||
                    employee.employee_name.toLowerCase().includes(searchTerm) ||
                    employee.employee_id.toLowerCase().includes(searchTerm) ||
                    employee.course.toLowerCase().includes(searchTerm);

                return statusMatch && searchMatch;
            });

            renderEmployeeList();
        }

        // Scheduler functions
        async function checkSchedulerStatus() {
            try {
                const response = await fetch('absence_scheduler.php?mode=status');
                const data = await response.json();

                Swal.fire({
                    icon: 'info',
                    title: 'Scheduler Status',
                    html: `
                        <div class="text-left">
                            <p><strong>Current Time:</strong> ${data.current_time}</p>
                            <p><strong>Should Run Now:</strong> ${data.should_run_now ? 'Yes' : 'No'}</p>
                            <p><strong>Business Hours:</strong> ${data.business_hours.start} - ${data.business_hours.end}</p>
                            <p><strong>Run Interval:</strong> ${data.business_hours.interval}</p>
                            ${data.last_run ? `
                                <hr>
                                <p><strong>Last Run:</strong> ${data.last_run.last_run}</p>
                                <p><strong>Status:</strong> <span class="badge bg-${data.last_run.status === 'completed' ? 'success' : 'danger'}">${data.last_run.status}</span></p>
                                <p><strong>Processed:</strong> ${data.last_run.total_processed} employees</p>
                                <p><strong>Marked Absent:</strong> ${data.last_run.marked_absent}</p>
                            ` : '<p><em>No previous runs found</em></p>'}
                        </div>
                    `,
                    width: 600
                });
            } catch (error) {
                Swal.fire('Error', 'Failed to get scheduler status', 'error');
            }
        }

        async function runSchedulerNow() {
            const result = await Swal.fire({
                title: 'Run Scheduler Now?',
                text: 'This will manually trigger the automatic absence processing.',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'Yes, run it!',
                cancelButtonText: 'Cancel'
            });

            if (result.isConfirmed) {
                try {
                    Swal.fire({
                        title: 'Processing...',
                        text: 'Running automatic absence processing',
                        allowOutsideClick: false,
                        didOpen: () => Swal.showLoading()
                    });

                    const response = await fetch('absence_scheduler.php?mode=run');
                    const data = await response.json();

                    Swal.fire({
                        icon: data.error ? 'error' : 'success',
                        title: data.error ? 'Error' : 'Scheduler Completed',
                        html: `
                            <div class="text-left">
                                ${data.error ? `
                                    <p><strong>Error:</strong> ${data.message}</p>
                                ` : `
                                    <p><strong>Execution Time:</strong> ${data.execution_time} seconds</p>
                                    <p><strong>Total Employees:</strong> ${data.total_employees}</p>
                                    <p><strong>Already Completed:</strong> ${data.already_completed}</p>
                                    <p><strong>Already Recorded:</strong> ${data.already_recorded}</p>
                                    <p><strong>Marked Absent:</strong> ${data.marked_absent}</p>
                                    <p><strong>Errors:</strong> ${data.errors}</p>
                                `}
                            </div>
                        `
                    });

                    // Refresh data after scheduler run
                    if (!data.error) {
                        loadEmployeeData();
                    }
                } catch (error) {
                    Swal.fire('Error', 'Failed to run scheduler: ' + error.message, 'error');
                }
            }
        }

        // Other functions
        function refreshData() {
            document.getElementById('loadingSpinner').style.display = 'block';
            document.getElementById('employeeList').style.display = 'none';
            loadEmployeeData();
        }

        function viewEmployeeDetails(tblEmpId) {
            const employee = employeesData.find(e => e.tbl_emp_id == tblEmpId);
            if (!employee) return;

            Swal.fire({
                title: employee.employee_name,
                html: `
                    <div class="text-left">
                        <p><strong>Employee ID:</strong> ${employee.employee_id}</p>
                        <p><strong>Course:</strong> ${employee.course}</p>
                        <p><strong>Type:</strong> ${employee.employee_type}</p>
                        <p><strong>Date Started:</strong> ${employee.date_started}</p>
                        <hr>
                        <p><strong>Required Hours:</strong> ${employee.required_hours}</p>
                        <p><strong>Completed Hours:</strong> ${employee.total_hours}</p>
                        <p><strong>Remaining Hours:</strong> ${employee.remaining_hours}</p>
                        <p><strong>Progress:</strong> ${employee.completion_percentage}%</p>
                        <hr>
                        <p><strong>Days Present:</strong> ${employee.total_days_present}</p>
                        <p><strong>Days Absent:</strong> ${employee.total_days_absent}</p>
                        <p><strong>Last Attendance:</strong> ${employee.last_attendance_date || 'Never'}</p>
                    </div>
                `,
                width: 500
            });
        }

        async function markAsAbsent(tblEmpId) {
            const employee = employeesData.find(e => e.tbl_emp_id == tblEmpId);
            if (!employee) return;

            const result = await Swal.fire({
                title: 'Mark as Absent?',
                text: `Mark ${employee.employee_name} as absent for today?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, mark absent',
                cancelButtonText: 'Cancel'
            });

            if (result.isConfirmed) {
                // Implementation would call backend to mark as absent
                Swal.fire('Success', 'Employee marked as absent', 'success');
                refreshData();
            }
        }

        function exportData() {
            // Implementation for exporting data
            Swal.fire('Info', 'Export functionality coming soon', 'info');
        }

        function viewLogs() {
            // Implementation for viewing logs
            Swal.fire('Info', 'Log viewer coming soon', 'info');
        }
    </script>
</body>
</html>
