<?php
/**
 * Get Employees Status API
 * Returns all employees with their attendance status and hours completion
 */

header('Content-Type: application/json');
date_default_timezone_set('Asia/Manila');

// Database connection
try {
    $connect = new PDO("mysql:host=localhost;dbname=attendance_monitoring_db", "root", "");
    $connect->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => true, 'message' => 'Database connection failed: ' . $e->getMessage()]);
    exit;
}

/**
 * Get all employees with their current status
 */
function getAllEmployeesStatus($connect) {
    $currentDate = date('Y-m-d');
    $currentTime = date('H:i:s');

    try {
        // Get all active employees
        $stmt = $connect->prepare("
            SELECT
                e.tbl_emp_id,
                e.employee_id,
                e.first_name,
                e.middle_initial,
                e.surname,
                e.no_hours_required,
                e.course,
                e.employee_type,
                e.date_started,
                e.date_registered
            FROM tbl_employee e
            WHERE e.user_role = 'Employee'
            ORDER BY e.employee_id
        ");
        $stmt->execute();
        $employees = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $result = [
            'success' => true,
            'current_date' => $currentDate,
            'current_time' => $currentTime,
            'total_employees' => count($employees),
            'employees' => []
        ];

        foreach ($employees as $employee) {
            $employeeStatus = getEmployeeStatus($connect, $employee, $currentDate);
            $result['employees'][] = $employeeStatus;
        }

        return $result;

    } catch (PDOException $e) {
        return [
            'success' => false,
            'error' => 'Database error: ' . $e->getMessage()
        ];
    }
}

/**
 * Get individual employee status
 */
function getEmployeeStatus($connect, $employee, $currentDate) {
    $tblEmpId = $employee['tbl_emp_id'];
    $employeeName = trim($employee['first_name'] . ' ' .
                        ($employee['middle_initial'] ? $employee['middle_initial'] . ' ' : '') .
                        $employee['surname']);

    // Get total hours completed
    $hoursData = getTotalHours($connect, $tblEmpId);

    // Get today's attendance
    $todayAttendance = getTodayAttendance($connect, $tblEmpId, $currentDate);

    // Calculate completion status
    $requiredHours = $employee['no_hours_required'] ?? 0;
    $totalHours = $hoursData['total_hours'];
    $isCompleted = $totalHours >= $requiredHours;
    $completionPercentage = $requiredHours > 0 ? round(($totalHours / $requiredHours) * 100, 1) : 0;
    $remainingHours = max(0, $requiredHours - $totalHours);

    // Determine current status
    $status = determineEmployeeStatus($todayAttendance, $isCompleted);

    return [
        'tbl_emp_id' => $tblEmpId,
        'employee_id' => $employee['employee_id'],
        'employee_name' => $employeeName,
        'first_name' => $employee['first_name'],
        'middle_initial' => $employee['middle_initial'],
        'surname' => $employee['surname'],
        'course' => $employee['course'],
        'employee_type' => $employee['employee_type'],
        'date_started' => $employee['date_started'],
        'date_registered' => $employee['date_registered'],

        // Hours information
        'required_hours' => $requiredHours,
        'total_hours' => round($totalHours, 2),
        'remaining_hours' => round($remainingHours, 2),
        'completion_percentage' => $completionPercentage,
        'is_completed' => $isCompleted,

        // Today's attendance
        'today_attendance' => $todayAttendance,
        'current_status' => $status,

        // Additional stats
        'total_days_present' => $hoursData['days_present'],
        'total_days_absent' => $hoursData['days_absent'],
        'last_attendance_date' => $hoursData['last_attendance_date']
    ];
}

/**
 * Get total hours for employee
 */
function getTotalHours($connect, $tblEmpId) {
    try {
        $stmt = $connect->prepare("
            SELECT
                SUM(CASE WHEN is_absent = 0 THEN total_daily_hours ELSE 0 END) as total_hours,
                COUNT(CASE WHEN is_absent = 0 THEN 1 END) as days_present,
                COUNT(CASE WHEN is_absent = 1 THEN 1 END) as days_absent,
                MAX(CASE WHEN is_absent = 0 THEN attendance_date END) as last_attendance_date
            FROM tbl_attendance
            WHERE tbl_emp_id = ?
        ");
        $stmt->execute([$tblEmpId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return [
            'total_hours' => $result['total_hours'] ?? 0,
            'days_present' => $result['days_present'] ?? 0,
            'days_absent' => $result['days_absent'] ?? 0,
            'last_attendance_date' => $result['last_attendance_date']
        ];

    } catch (PDOException $e) {
        error_log("Error getting total hours for employee ID {$tblEmpId}: " . $e->getMessage());
        return [
            'total_hours' => 0,
            'days_present' => 0,
            'days_absent' => 0,
            'last_attendance_date' => null
        ];
    }
}

/**
 * Get today's attendance for employee
 */
function getTodayAttendance($connect, $tblEmpId, $currentDate) {
    try {
        $stmt = $connect->prepare("
            SELECT
                tbl_attendance_id,
                attendance_date,
                morning_time_in,
                morning_time_out,
                afternoon_time_in,
                afternoon_time_out,
                morning_hours,
                afternoon_hours,
                total_daily_hours,
                is_absent,
                allow_overtime,
                comments
            FROM tbl_attendance
            WHERE tbl_emp_id = ? AND DATE(attendance_date) = ?
        ");
        $stmt->execute([$tblEmpId, $currentDate]);
        $attendance = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$attendance) {
            return [
                'has_record' => false,
                'is_absent' => false,
                'status' => 'no_record'
            ];
        }

        // Format times for display
        $formatTime = function($time) {
            return $time ? date('h:i A', strtotime($time)) : null;
        };

        return [
            'has_record' => true,
            'attendance_id' => $attendance['tbl_attendance_id'],
            'attendance_date' => $attendance['attendance_date'],
            'morning_time_in' => $formatTime($attendance['morning_time_in']),
            'morning_time_out' => $formatTime($attendance['morning_time_out']),
            'afternoon_time_in' => $formatTime($attendance['afternoon_time_in']),
            'afternoon_time_out' => $formatTime($attendance['afternoon_time_out']),
            'morning_hours' => $attendance['morning_hours'],
            'afternoon_hours' => $attendance['afternoon_hours'],
            'total_daily_hours' => $attendance['total_daily_hours'],
            'is_absent' => $attendance['is_absent'] == 1,
            'allow_overtime' => $attendance['allow_overtime'] == 1,
            'comments' => $attendance['comments'],
            'status' => $attendance['is_absent'] == 1 ? 'absent' : 'present'
        ];

    } catch (PDOException $e) {
        error_log("Error getting today's attendance for employee ID {$tblEmpId}: " . $e->getMessage());
        return [
            'has_record' => false,
            'is_absent' => false,
            'status' => 'error',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Determine employee's current status
 */
function determineEmployeeStatus($todayAttendance, $isCompleted) {
    if ($isCompleted) {
        return [
            'code' => 'completed',
            'label' => 'Hours Completed',
            'description' => 'Employee has completed required hours',
            'color' => 'success'
        ];
    }

    if (!$todayAttendance['has_record']) {
        return [
            'code' => 'no_record',
            'label' => 'No Record',
            'description' => 'No attendance record for today',
            'color' => 'warning'
        ];
    }

    if ($todayAttendance['is_absent']) {
        return [
            'code' => 'absent',
            'label' => 'Absent',
            'description' => 'Marked as absent for today',
            'color' => 'danger'
        ];
    }

    // Check attendance progress
    $hasTimeIn = $todayAttendance['morning_time_in'] || $todayAttendance['afternoon_time_in'];
    $hasTimeOut = $todayAttendance['morning_time_out'] || $todayAttendance['afternoon_time_out'];

    if ($hasTimeIn && $hasTimeOut) {
        return [
            'code' => 'completed_day',
            'label' => 'Day Complete',
            'description' => 'Attendance completed for today',
            'color' => 'success'
        ];
    } elseif ($hasTimeIn) {
        return [
            'code' => 'in_progress',
            'label' => 'In Progress',
            'description' => 'Currently logged in',
            'color' => 'info'
        ];
    } else {
        return [
            'code' => 'present',
            'label' => 'Present',
            'description' => 'Present but no time recorded',
            'color' => 'primary'
        ];
    }
}

// Handle different request types
$action = $_GET['action'] ?? 'all';

switch ($action) {
    case 'all':
        $result = getAllEmployeesStatus($connect);
        break;

    case 'single':
        $employeeId = $_GET['employee_id'] ?? null;
        if (!$employeeId) {
            $result = ['success' => false, 'error' => 'Employee ID required'];
        } else {
            // Get single employee (implementation would be similar)
            $result = ['success' => false, 'error' => 'Single employee endpoint not implemented yet'];
        }
        break;

    default:
        $result = ['success' => false, 'error' => 'Invalid action'];
}

echo json_encode($result, JSON_PRETTY_PRINT);
?>
