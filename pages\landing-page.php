<?php
// landing-page.php
date_default_timezone_set('Asia/Manila');
try {
    $connect = new PDO("mysql:host=localhost;dbname=attendance_monitoring_db", "root", "");
    $connect->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    error_log("Database connection successful.");
    echo "<script>console.log('Database connection successful.');</script>";
} catch (PDOException $e) {
    error_log("Database connection failed: " . $e->getMessage());
    echo "<script>console.error('Database connection failed: " . addslashes($e->getMessage()) . "');</script>";
    die("Database connection failed: " . $e->getMessage());
}

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// AUTO-ABSENT PROCESSING
// Check if it's end of business day (after 5 PM) and if we haven't already processed auto-absent for today
$currentHour = (int)date('H');
$today = date('Y-m-d');
$autoAbsentKey = 'auto_absent_processed_' . $today;

// Only run auto-absent if it's after 5 PM and we haven't processed it yet today
if ($currentHour >= 17 && !isset($_SESSION[$autoAbsentKey])) {
    // Check if today is a holiday
    $holiday = checkIfHoliday($connect, $today);

    if (!$holiday) {
        // Not a holiday, proceed with auto-absent processing
        $processResult = processAutoAbsent($connect);

        // Mark as processed for today to prevent running again
        $_SESSION[$autoAbsentKey] = true;

        // Set a flash message if in admin or viewing context
        if (isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'admin') {
            $_SESSION['auto_absent_result'] = $processResult;
        }

        // Log the processing result
        error_log("Auto-absent processing on landing page: " . json_encode($processResult));
    }
}

/**
 * Process auto-absent for all employees
 */
function processAutoAbsent($connect) {
    $result = [
        'total' => 0,
        'skipped_hours_completed' => 0,
        'already_recorded' => 0,
        'marked_absent' => 0,
        'errors' => 0
    ];

    try {
        // Get all active employees
        $empStmt = $connect->prepare("SELECT * FROM tbl_employee WHERE is_active = 1");
        $empStmt->execute();
        $employees = $empStmt->fetchAll(PDO::FETCH_ASSOC);

        $result['total'] = count($employees);

        // Process each employee
        foreach ($employees as $employee) {
            $employeeId = $employee['tbl_emp_id'];
            $employeeName = $employee['first_name'] . ' ' . ($employee['middle_initial'] ? $employee['middle_initial'] . ' ' : '') . $employee['surname'];

            // Skip employees who have completed their required hours
            if (hasCompletedRequiredHours($connect, $employeeId)) {
                $result['skipped_hours_completed']++;
                continue;
            }

            // Check if employee has attendance record for today
            $checkStmt = $connect->prepare("SELECT * FROM tbl_attendance WHERE tbl_emp_id = :employeeId AND DATE(attendance_date) = CURDATE()");
            $checkStmt->bindParam(':employeeId', $employeeId);
            $checkStmt->execute();
            $attendanceRecord = $checkStmt->fetch(PDO::FETCH_ASSOC);

            if ($attendanceRecord) {
                // Employee already has a record for today
                $result['already_recorded']++;
                continue;
            }

            // No attendance record found - mark as absent
            try {
                $connect->beginTransaction();
                $insertStmt = $connect->prepare("INSERT INTO tbl_attendance
                    (attendance_date, morning_time_in, morning_time_out, afternoon_time_in, afternoon_time_out,
                    morning_hours, afternoon_hours, total_daily_hours, is_absent, tbl_emp_id)
                    VALUES (NOW(), NULL, NULL, NULL, NULL, 0, 0, 0, 1, ?)");
                $insertStmt->execute([$employeeId]);
                $connect->commit();

                $result['marked_absent']++;
            } catch (PDOException $e) {
                $connect->rollBack();
                error_log("Error marking $employeeName (ID: $employeeId) as absent: " . $e->getMessage());
                $result['errors']++;
            }
        }

    } catch (PDOException $e) {
        error_log("Error in auto-absent process: " . $e->getMessage());
        $result['errors']++;
    }

    return $result;
}

// Process form submission if it exists
if (isset($_POST['employeeId'])) {
    // Check if today is a holiday before processing the form
    $today = date('Y-m-d');
    $holiday = checkIfHoliday($connect, $today);

    if ($holiday) {
        // Store the result in session for display after redirect
        $_SESSION['attendance_result'] = [
            'processed' => true,
            'timestamp' => time(),
            'status' => 'error',
            'message' => 'Non-Working Day Detected',
            'details' => 'Today (' . date('F j, Y') . ') is a recognized holiday: ' . $holiday['holiday_name'] . '. In accordance with company policy, attendance records cannot be created for official holidays.'
        ];

        // Redirect to prevent form resubmission
        header('Location: landing-page.php');
        exit();
    } else {
        // Not a holiday, proceed with form submission
        processAttendanceForm($connect);
    }
}

function isWithinTimeRange($time, $start, $end, $overrideMinutes = 30)
{
    $time = DateTime::createFromFormat('H:i', $time);
    $start = DateTime::createFromFormat('H:i', $start);
    $end = DateTime::createFromFormat('H:i', $end);

    // Always extend the range by the specified minutes (default 30)
    $startInterval = new DateInterval('PT' . $overrideMinutes . 'M');
    $endInterval = new DateInterval('PT' . $overrideMinutes . 'M');
    $start->sub($startInterval);
    $end->add($endInterval);

    echo "<script>console.log('Comparing time: " . $time->format('H:i') . " with range: " . $start->format('H:i') . " - " . $end->format('H:i') . "');</script>";

    return $time >= $start && $time <= $end;
}

function getTimeRangeMessage($currentTime)
{
    global $connect;

    // Get active time range from database
    try {
        $timeRangeStmt = $connect->prepare("SELECT * FROM tbl_time_ranges WHERE activate_this_timerange = 1 LIMIT 1");
        $timeRangeStmt->execute();
        $activeTimeRange = $timeRangeStmt->fetch(PDO::FETCH_ASSOC);

        if ($activeTimeRange) {
            $timeRanges = [
                'morning_in' => [date('H:i', strtotime($activeTimeRange['morning_time_in_start'])), date('H:i', strtotime($activeTimeRange['morning_time_in_end']))],
                'morning_out' => [date('H:i', strtotime($activeTimeRange['morning_time_out_start'])), date('H:i', strtotime($activeTimeRange['morning_time_out_end']))],
                'afternoon_in' => [date('H:i', strtotime($activeTimeRange['afternoon_time_in_start'])), date('H:i', strtotime($activeTimeRange['afternoon_time_in_end']))],
                'afternoon_out' => [date('H:i', strtotime($activeTimeRange['afternoon_time_out_start'])), date('H:i', strtotime($activeTimeRange['afternoon_time_out_end']))],
            ];
        } else {
            // Default values if no active time range is found
            $timeRanges = [
                'morning_in' => ['07:00', '08:30'],
                'morning_out' => ['12:00', '12:30'],
                'afternoon_in' => ['12:31', '13:30'],
                'afternoon_out' => ['17:00', '18:00'],
            ];
        }
    } catch (PDOException $e) {
        // Default values if there's an error
        $timeRanges = [
            'morning_in' => ['07:00', '08:30'],
            'morning_out' => ['12:00', '12:30'],
            'afternoon_in' => ['12:31', '13:30'],
            'afternoon_out' => ['17:00', '18:00'],
        ];
    }

    // Always use override minutes (30 minutes)
    $overrideMinutes = 30;

    foreach ($timeRanges as $session => $range) {
        if (isWithinTimeRange($currentTime, $range[0], $range[1], $overrideMinutes)) {
            return ucwords(str_replace('_', ' ', $session)) . " Session ({$range[0]} - {$range[1]})";
        }
    }

    // Determine the next session
    $currentDateTime = DateTime::createFromFormat('H:i', $currentTime);
    $nextSession = null;

    foreach ($timeRanges as $session => $range) {
        $start = DateTime::createFromFormat('H:i', $range[0]);
        if ($start > $currentDateTime) {
            $nextSession = ucwords(str_replace('_', ' ', $session)) . " Session ({$range[0]} - {$range[1]})";
            break;
        }
    }

    // If no session is found, default to the first morning session
    if (!$nextSession) {
        // Use the morning_in time range from the $timeRanges array
        $morningInStart = $timeRanges['morning_in'][0];
        $morningInEnd = $timeRanges['morning_in'][1];
        $nextSession = "Next Session: Morning In ($morningInStart - $morningInEnd)";
    }

    return $nextSession;
}

// Function to check if employee has completed required hours
function hasCompletedRequiredHours($connect, $tblEmpId) {
    try {
        // First, get the employee's required hours
        $empStmt = $connect->prepare("SELECT no_hours_required FROM tbl_employee WHERE tbl_emp_id = :empId");
        $empStmt->bindParam(":empId", $tblEmpId);
        $empStmt->execute();
        $employee = $empStmt->fetch(PDO::FETCH_ASSOC);

        if (!$employee || !isset($employee['no_hours_required'])) {
            return false; // Cannot determine if completed
        }

        $requiredHours = $employee['no_hours_required'];

        // Get total hours logged by employee
        $hoursStmt = $connect->prepare("SELECT SUM(total_daily_hours) as total_hours FROM tbl_attendance WHERE tbl_emp_id = :empId");
        $hoursStmt->bindParam(":empId", $tblEmpId);
        $hoursStmt->execute();
        $result = $hoursStmt->fetch(PDO::FETCH_ASSOC);

        $totalHours = $result['total_hours'] ?? 0;

        // Return true if total hours meets or exceeds required hours
        return $totalHours >= $requiredHours;
    } catch (PDOException $e) {
        error_log("Error checking completed hours: " . $e->getMessage());
        return false;
    }
}

// Function to process the form submission
function processAttendanceForm($connect)
{
    if (isset($_POST['employeeId']) && isset($_POST['form_token'])) {
        // Verify the form token to prevent duplicate submissions
        if (!isset($_SESSION['form_token']) || $_POST['form_token'] !== $_SESSION['form_token']) {
            // Invalid or expired token, redirect to prevent duplicate submission
            header('Location: landing-page.php');
            exit();
        }

        // Process the form data
        $employeeId = $_POST['employeeId'];
        $currentTime = date('H:i');

        // Get time range message (always with 30-minute override)
        $timeRangeMessage = getTimeRangeMessage($currentTime);

        // Get the current datetime
        $currentDatetime = date('Y-m-d H:i:s');

        // Generate a new token for the next submission
        $_SESSION['form_token'] = bin2hex(random_bytes(32));

        // Fetch employee details
        $stmt = $connect->prepare("SELECT * FROM tbl_employee WHERE employee_id = :employeeId");
        $stmt->bindParam(':employeeId', $employeeId);
        $stmt->execute();
        $employee = $stmt->fetch(PDO::FETCH_ASSOC);

        // Store the result in session for display after redirect
        $_SESSION['attendance_result'] = [
            'processed' => true,
            'timestamp' => time()
        ];

        if ($employee) {
            $tblEmpId = $employee['tbl_emp_id'];
            $employeeName = $employee['first_name'] . ' ' . ($employee['middle_initial'] ? $employee['middle_initial'] . ' ' : '') . $employee['surname'];

            // Check if employee has completed required hours
            if (hasCompletedRequiredHours($connect, $tblEmpId)) {
                $_SESSION['attendance_result']['status'] = 'info';
                $_SESSION['attendance_result']['message'] = 'Hours Requirement Completed';
                $_SESSION['attendance_result']['details'] = htmlspecialchars($employeeName, ENT_QUOTES, 'UTF-8') . ' has already completed the required hours.';

                // Redirect to prevent form resubmission
                header('Location: landing-page.php');
                exit();
            }

            // Check if a record exists for today
            $checkStmt = $connect->prepare("SELECT * FROM tbl_attendance WHERE tbl_emp_id = :employeeId AND DATE(attendance_date) = CURDATE()");
            $checkStmt->bindParam(':employeeId', $tblEmpId);
            $checkStmt->execute();
            $attendanceRecord = $checkStmt->fetch(PDO::FETCH_ASSOC);

            // If the employee is already marked as absent, prevent recording attendance
            if ($attendanceRecord && isset($attendanceRecord['is_absent']) && $attendanceRecord['is_absent'] == 1) {
                $_SESSION['attendance_result']['status'] = 'warning';
                $_SESSION['attendance_result']['message'] = 'Cannot Record Attendance';
                $_SESSION['attendance_result']['details'] = htmlspecialchars($employeeName, ENT_QUOTES, 'UTF-8') . ' is already marked as absent for today. Cannot record attendance.';

                // Redirect to prevent form resubmission
                header('Location: landing-page.php');
                exit();
            }

            // Determine the next time field to update based on current time
            $timeField = null;

            // Get active time range from database
            try {
                $timeRangeStmt = $connect->prepare("SELECT * FROM tbl_time_ranges WHERE activate_this_timerange = 1 LIMIT 1");
                $timeRangeStmt->execute();
                $activeTimeRange = $timeRangeStmt->fetch(PDO::FETCH_ASSOC);

                if ($activeTimeRange) {
                    $timeRanges = [
                        'morning_time_in' => [date('H:i', strtotime($activeTimeRange['morning_time_in_start'])), date('H:i', strtotime($activeTimeRange['morning_time_in_end']))],
                        'morning_time_out' => [date('H:i', strtotime($activeTimeRange['morning_time_out_start'])), date('H:i', strtotime($activeTimeRange['morning_time_out_end']))],
                        'afternoon_time_in' => [date('H:i', strtotime($activeTimeRange['afternoon_time_in_start'])), date('H:i', strtotime($activeTimeRange['afternoon_time_in_end']))],
                        'afternoon_time_out' => [date('H:i', strtotime($activeTimeRange['afternoon_time_out_start'])), date('H:i', strtotime($activeTimeRange['afternoon_time_out_end']))],
                    ];
                } else {
                    // Default values if no active time range is found
                    $timeRanges = [
                        'morning_time_in' => ['07:00', '08:30'],
                        'morning_time_out' => ['12:00', '12:30'],
                        'afternoon_time_in' => ['12:31', '13:30'],
                        'afternoon_time_out' => ['17:00', '18:00'],
                    ];
                }
            } catch (PDOException $e) {
                // Default values if there's an error
                $timeRanges = [
                    'morning_time_in' => ['07:00', '08:30'],
                    'morning_time_out' => ['12:00', '12:30'],
                    'afternoon_time_in' => ['12:31', '13:30'],
                    'afternoon_time_out' => ['17:00', '18:00'],
                ];
            }

            // Always use 30 minutes override
            $overrideMinutes = 30;

            // Determine which time field to update based on current time
            foreach ($timeRanges as $field => $range) {
                if (isWithinTimeRange($currentTime, $range[0], $range[1], $overrideMinutes)) {
                    $timeField = $field;
                    break;
                }
            }

            // If timeField is null, it means the current time is outside all allowed ranges
            if ($timeField === null) {
                // Get current hour for auto-absent logic
                $currentHour = (int)date('H');

                // Only auto-mark as absent if between 8am and 5pm (8-17)
                // Don't mark as absent if recording between 6am and 8am (6-8)
                if ($currentHour >= 8 && $currentHour <= 17 && (!$attendanceRecord || empty($attendanceRecord['morning_time_in']))) {
                    // During business hours and no attendance recorded, mark as absent
                    if ($attendanceRecord) {
                        // Update existing record to mark as absent
                        $updateStmt = $connect->prepare("UPDATE tbl_attendance SET
                            is_absent = 1
                            WHERE tbl_attendance_id = ?");
                        try {
                            $connect->beginTransaction();
                            $updateStmt->execute([$attendanceRecord['tbl_attendance_id']]);
                            $connect->commit();

                            $_SESSION['attendance_result']['status'] = 'info';
                            $_SESSION['attendance_result']['message'] = 'Auto-Marked as Absent';
                            $_SESSION['attendance_result']['details'] = htmlspecialchars($employeeName, ENT_QUOTES, 'UTF-8') . ' has been automatically marked as absent for today.';
                        } catch (PDOException $e) {
                            $connect->rollBack();
                            $_SESSION['attendance_result']['status'] = 'error';
                            $_SESSION['attendance_result']['message'] = 'Error';
                            $_SESSION['attendance_result']['details'] = 'Error marking absence: ' . $e->getMessage();
                        }
                    } else {
                        // Create a new record marked as absent
                        try {
                            $connect->beginTransaction();
                            $insertStmt = $connect->prepare("INSERT INTO tbl_attendance
                                (attendance_date, morning_time_in, morning_time_out, afternoon_time_in, afternoon_time_out,
                                morning_hours, afternoon_hours, total_daily_hours, is_absent, tbl_emp_id)
                                VALUES (NOW(), NULL, NULL, NULL, NULL, 0, 0, 0, 1, ?)");
                            $insertStmt->execute([$tblEmpId]);
                            $connect->commit();

                            $_SESSION['attendance_result']['status'] = 'info';
                            $_SESSION['attendance_result']['message'] = 'Auto-Marked as Absent';
                            $_SESSION['attendance_result']['details'] = htmlspecialchars($employeeName, ENT_QUOTES, 'UTF-8') . ' has been automatically marked as absent for today.';
                        } catch (PDOException $e) {
                            $connect->rollBack();
                            $_SESSION['attendance_result']['status'] = 'error';
                            $_SESSION['attendance_result']['message'] = 'Error';
                            $_SESSION['attendance_result']['details'] = 'Error marking absence: ' . $e->getMessage();
                        }
                    }
                } else if ($currentHour >= 6 && $currentHour < 8) {
                    // Early morning attendance (6am-8am) - allow recording outside regular time ranges
                    // Determine which time field to use based on the hour (always morning time in for early hours)
                    $timeField = 'morning_time_in';

                    // Process as regular attendance with the determined time field
                    if ($attendanceRecord) {
                        // Update existing record
                        $attendanceStmt = $connect->prepare("UPDATE tbl_attendance SET $timeField = :currentDatetime WHERE tbl_attendance_id = :attendanceId");
                        $attendanceStmt->bindParam(':attendanceId', $attendanceRecord['tbl_attendance_id']);
                        $attendanceStmt->bindParam(':currentDatetime', $currentDatetime);
                    } else {
                        // Insert new record
                        $attendanceStmt = $connect->prepare("INSERT INTO tbl_attendance (attendance_date, tbl_emp_id, $timeField) VALUES (NOW(), :employeeId, :currentDatetime)");
                        $attendanceStmt->bindParam(':employeeId', $tblEmpId);
                        $attendanceStmt->bindParam(':currentDatetime', $currentDatetime);
                    }

                    try {
                        $connect->beginTransaction();
                        $attendanceStmt->execute();
                        $connect->commit();

                        // Calculate hours and update the attendance record
                        $attendanceId = $attendanceRecord['tbl_attendance_id'] ?? $connect->lastInsertId();
                        calculateAndUpdateAttendance($connect, $attendanceId);

                        // Store success information in session
                        $currentTimeOnly = date('h:i:s A'); // Format to show only the time in 12-hour format
                        $_SESSION['attendance_result']['status'] = 'success';
                        $_SESSION['attendance_result']['message'] = 'Early Attendance Recorded';
                        $_SESSION['attendance_result']['details'] = 'Welcome ' . htmlspecialchars($employeeName, ENT_QUOTES, 'UTF-8') . '! Early morning time in recorded at ' . $currentTimeOnly;
                    } catch (PDOException $e) {
                        $connect->rollBack();
                        error_log("SQL Query failed: " . $e->getMessage());

                        $_SESSION['attendance_result']['status'] = 'error';
                        $_SESSION['attendance_result']['message'] = 'Error';
                        $_SESSION['attendance_result']['details'] = 'Error recording attendance: ' . $e->getMessage();
                    }
                } else {
                    $_SESSION['attendance_result']['status'] = 'error';
                    $_SESSION['attendance_result']['message'] = 'Outside Time Range';
                    $_SESSION['attendance_result']['details'] = 'Cannot record attendance at this time. You are outside the allowed time ranges (±30 minutes).';
                }

                // Redirect to prevent form resubmission
                header('Location: landing-page.php');
                exit();
            }

            // This is the missing code section that handles normal attendance recording
            // when timeField is not null (i.e., the current time is within one of the allowed ranges)
            if ($timeField) {
                // Check for time-out without time-in
                if ($timeField == 'morning_time_out' || $timeField == 'afternoon_time_out') {
                    $correspondingInField = str_replace('_out', '_in', $timeField);

                    if ($attendanceRecord && empty($attendanceRecord[$correspondingInField])) {
                        $sessionType = ($timeField == 'morning_time_out') ? 'morning' : 'afternoon';
                        $_SESSION['attendance_result']['status'] = 'warning';
                        $_SESSION['attendance_result']['message'] = 'Cannot Record Time-Out';
                        $_SESSION['attendance_result']['details'] = 'Cannot record ' . $sessionType . ' time-out without ' . $sessionType . ' time-in.';

                        // Redirect to prevent form resubmission
                        header('Location: landing-page.php');
                        exit();
                    }
                }

                // Check if the specific time field is already filled
                if ($attendanceRecord && !empty($attendanceRecord[$timeField])) {
                    $_SESSION['attendance_result']['status'] = 'info';
                    $_SESSION['attendance_result']['message'] = 'Already Recorded';
                    $_SESSION['attendance_result']['details'] = 'The ' . str_replace('_', ' ', $timeField) . ' for ' . htmlspecialchars($employeeName, ENT_QUOTES, 'UTF-8') . ' is already recorded for today.';

                    // Redirect to prevent form resubmission
                    header('Location: landing-page.php');
                    exit();
                }

                try {
                    $connect->beginTransaction();

                    if ($attendanceRecord) {
                        // Update existing record
                        $attendanceStmt = $connect->prepare("UPDATE tbl_attendance SET $timeField = :currentDatetime WHERE tbl_attendance_id = :attendanceId");
                        $attendanceStmt->bindParam(':attendanceId', $attendanceRecord['tbl_attendance_id']);
                        $attendanceStmt->bindParam(':currentDatetime', $currentDatetime);
                        $attendanceStmt->execute();
                        $attendanceId = $attendanceRecord['tbl_attendance_id'];
                    } else {
                        // Insert new record with is_absent defaulting to 0
                        $attendanceStmt = $connect->prepare("INSERT INTO tbl_attendance
                            (attendance_date, tbl_emp_id, $timeField, is_absent)
                            VALUES (NOW(), :employeeId, :currentDatetime, 0)");
                        $attendanceStmt->bindParam(':employeeId', $tblEmpId);
                        $attendanceStmt->bindParam(':currentDatetime', $currentDatetime);
                        $attendanceStmt->execute();
                        $attendanceId = $connect->lastInsertId();
                    }

                    $connect->commit();

                    // Log success
                    error_log("Attendance recorded successfully for employee ID: $employeeId, field: $timeField, record ID: $attendanceId");

                    // Calculate hours and update the attendance record
                    calculateAndUpdateAttendance($connect, $attendanceId);

                    // Get field name for display
                    $fieldName = str_replace('_', ' ', $timeField);
                    $fieldName = ucwords($fieldName);

                    // Format time for display
                    $displayTime = date('h:i:s A', strtotime($currentDatetime));

                    // Store success information in session
                    $_SESSION['attendance_result']['status'] = 'success';
                    $_SESSION['attendance_result']['message'] = 'Attendance Recorded';
                    $_SESSION['attendance_result']['details'] = 'Welcome ' . htmlspecialchars($employeeName, ENT_QUOTES, 'UTF-8') . '! ' . $fieldName . ' recorded at ' . $displayTime;

                } catch (PDOException $e) {
                    $connect->rollBack();
                    error_log("SQL Query failed: " . $e->getMessage());

                    $_SESSION['attendance_result']['status'] = 'error';
                    $_SESSION['attendance_result']['message'] = 'Database Error';
                    $_SESSION['attendance_result']['details'] = 'Error recording attendance: ' . $e->getMessage();
                }

                // Redirect to prevent form resubmission
                header('Location: landing-page.php');
                exit();
            }

            // Employee not found
            $_SESSION['attendance_result']['status'] = 'error';
            $_SESSION['attendance_result']['message'] = 'Employee Not Found';
            $_SESSION['attendance_result']['details'] = 'No employee with the ID ' . htmlspecialchars($employeeId, ENT_QUOTES, 'UTF-8') . ' was found. Time Range: ' . $timeRangeMessage;

            // Redirect to prevent form resubmission
            header('Location: landing-page.php');
            exit();
        }
    }
}

// Function to check if a date is a holiday
function checkIfHoliday($connect, $date) {
    try {
        // Check for exact date match (both recurring and non-recurring holidays)
        $query = "
            SELECT
                id,
                holiday_name,
                holiday_date,
                is_recurring
            FROM
                tbl_holidays
            WHERE
                holiday_date = :date
                OR (
                    is_recurring = 1
                    AND MONTH(holiday_date) = MONTH(:date)
                    AND DAY(holiday_date) = DAY(:date)
                )
        ";

        $stmt = $connect->prepare($query);
        $stmt->bindParam(':date', $date);
        $stmt->execute();

        $holiday = $stmt->fetch(PDO::FETCH_ASSOC);

        return $holiday ?: false;
    } catch (Exception $e) {
        error_log("Error checking holiday: " . $e->getMessage());
        return false;
    }
}

function calculateAndUpdateAttendance($connect, $attendanceId)
{
    // Fetch the attendance record to calculate hours
    $stmt = $connect->prepare("SELECT * FROM tbl_attendance WHERE tbl_attendance_id = :attendanceId");
    $stmt->bindParam(':attendanceId', $attendanceId);
    $stmt->execute();
    $attendance = $stmt->fetch(PDO::FETCH_ASSOC);

    // Check if attendance record exists
    if ($attendance === false) {
        error_log("No attendance record found for ID: $attendanceId");
        echo "<script>console.error('No attendance record found for ID: $attendanceId');</script>";
        return;
    }

    $morningDuration = calculateHours($attendance['morning_time_in'] ?? null, $attendance['morning_time_out'] ?? null);
    $afternoonDuration = calculateHours($attendance['afternoon_time_in'] ?? null, $attendance['afternoon_time_out'] ?? null);
    $totalDuration = $morningDuration + $afternoonDuration;

    // Update the attendance record with calculated hours
    $updateStmt = $connect->prepare("UPDATE tbl_attendance SET morning_hours = :morningDuration, afternoon_hours = :afternoonDuration, total_daily_hours = :totalDuration WHERE tbl_attendance_id = :attendanceId");
    $updateStmt->bindParam(':morningDuration', $morningDuration);
    $updateStmt->bindParam(':afternoonDuration', $afternoonDuration);
    $updateStmt->bindParam(':totalDuration', $totalDuration);
    $updateStmt->bindParam(':attendanceId', $attendanceId);

    try {
        $updateStmt->execute();
        error_log("Attendance record updated successfully for ID: $attendanceId");
    } catch (PDOException $e) {
        error_log("Error updating attendance record: " . $e->getMessage());
        echo "<script>console.error('Error updating attendance record: " . addslashes($e->getMessage()) . "');</script>";
    }
}

// Add this function near the other calculation functions
function calculateHours($timeIn, $timeOut)
{
    if (empty($timeIn) || empty($timeOut)) {
        return 0;
    }

    $timeInObj = new DateTime($timeIn);
    $timeOutObj = new DateTime($timeOut);

    // Calculate the difference
    $interval = $timeInObj->diff($timeOutObj);

    // Convert to decimal hours
    $hours = $interval->h + ($interval->i / 60);

    error_log("Calculated hours between $timeIn and $timeOut: $hours");
    return $hours;
}
?>



<!doctype html>
<html lang="en" dir="ltr">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="OJT Monitoring System is a platform designed to track and manage on-the-job training programs efficiently.">
    <meta name="author" content="Your Name or Organization">

    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap" />
    <link rel="stylesheet" href="../assets/MDB5-STANDARD-UI-KIT/css/mdb.min.css" />
    <link rel="stylesheet" href="../assets/boxicons-master/css/boxicons.min.css">
    <script src="../assets/js/JsBarcode.all.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <title>OJT Monitoring System</title>

    <style>
        html,
        body {
            height: 100%;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        *,
        *::before,
        *::after {
            box-sizing: inherit;
        }

        body {
            display: flex;
            flex-direction: column;
            background-image: url("../assets/img/landing_page.png");
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            zoom: 70%;
            min-height: 100vh;
        }

        .content-wrapper {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .glassmorphism-form {
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
            width: 500px;
            max-width: 90%;
            height: auto;
            position: absolute;
            left: 200px;
            bottom: 30px;
        }

        .glassmorphism-form.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .glassmorphism-form .form-control {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            border: 2px solid rgba(183, 183, 183, 0.8);
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.2);
            color: #000;
        }

        .glassmorphism-form .btn {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.2);
            color: #000;
            cursor: pointer;
        }

        #reader {
            width: 100%;
        }

        footer {
            width: 100%;
            margin: 0;
            padding: 10px 0;
            background-color: #f5f5f9;
            text-align: center;
            transition: opacity 0.3s ease;
        }

        .footer-date-time {
            font-weight: bold;
        }

        footer.hidden,
        .glassmorphism-form.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .time-ranges-info {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 15px;
        }

        .time-ranges-info h5 {
            margin-top: 0;
            color: #333;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .time-ranges-info ul {
            list-style-type: none;
            padding-left: 5px;
            margin-bottom: 0;
        }

        .time-ranges-info li {
            color: #333;
            margin-bottom: 5px;
            font-size: 0.9rem;
        }

        /* Enhanced validation styles */
        .form-control.is-valid {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        .form-control.is-invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
            animation: shake 0.5s ease-in-out;
        }

        .valid-feedback {
            display: block;
            color: #28a745;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        .invalid-feedback {
            display: block;
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        /* Loading spinner for submit button */
        .btn-loading {
            position: relative;
            pointer-events: none;
        }

        .btn-loading::after {
            content: "";
            position: absolute;
            width: 16px;
            height: 16px;
            margin: auto;
            border: 2px solid transparent;
            border-top-color: #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Enhanced SweetAlert custom styles */
        .swal2-popup.swal2-toast {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .swal2-icon.swal2-success .swal2-success-ring {
            border-color: #28a745;
        }

        .swal2-icon.swal2-error .swal2-x-mark-line-left,
        .swal2-icon.swal2-error .swal2-x-mark-line-right {
            background-color: #dc3545;
        }

        /* Custom SweetAlert popup styles */
        .swal2-holiday-popup {
            border-radius: 15px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
        }

        .swal2-popup .alert {
            border-radius: 8px;
            margin: 15px 0;
            padding: 12px 15px;
        }

        .swal2-popup .alert-info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }

        .swal2-popup .alert-danger {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .swal2-popup .alert-warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }

        .swal2-popup .alert-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .glassmorphism-form {
                left: 20px;
                right: 20px;
                width: auto;
                bottom: 20px;
            }

            .swal2-popup {
                width: 90% !important;
                margin: 0 auto;
            }
        }
    </style>


</head>

<body>
    <script src="https://unpkg.com/html5-qrcode" type="text/javascript"></script>
    <div class="content-wrapper">
        <section>
            <form class="glassmorphism-form" action="landing-page.php" method="POST" id="attendanceForm">
                <h2 class="text-center py-2" style="color: #000;"><b>Monitoring System</b></h2>
                <label class="mt-2" for="employeeId" style="color: #000;">Employee ID</label>
                <input type="text" id="employeeId" name="employeeId" class="form-control mb-3" placeholder="Enter Employee ID" required
                       pattern="[A-Za-z0-9\-_]{3,20}"
                       title="Employee ID must be 3-20 characters long and contain only letters, numbers, hyphens, or underscores"
                       maxlength="20"
                       autocomplete="off">
                <div id="employeeIdFeedback" class="invalid-feedback" style="display: none;"></div>

                <!-- Display Time Ranges Information -->
                <div class="time-ranges-info">
                    <h5 class="text-center">Attendance Time Ranges</h5>
                    <ul>
                        <?php
                        // Get active time range from database
                        try {
                            $timeRangeStmt = $connect->prepare("SELECT * FROM tbl_time_ranges WHERE activate_this_timerange = 1 LIMIT 1");
                            $timeRangeStmt->execute();
                            $activeTimeRange = $timeRangeStmt->fetch(PDO::FETCH_ASSOC);

                            if ($activeTimeRange) {
                                // Format times for display in 12-hour format
                                echo "<li><strong>Morning Time In:</strong> " .
                                    date('h:i A', strtotime($activeTimeRange['morning_time_in_start'])) . " - " .
                                    date('h:i A', strtotime($activeTimeRange['morning_time_in_end'])) . "</li>";

                                echo "<li><strong>Morning Time Out:</strong> " .
                                    date('h:i A', strtotime($activeTimeRange['morning_time_out_start'])) . " - " .
                                    date('h:i A', strtotime($activeTimeRange['morning_time_out_end'])) . "</li>";

                                echo "<li><strong>Afternoon Time In:</strong> " .
                                    date('h:i A', strtotime($activeTimeRange['afternoon_time_in_start'])) . " - " .
                                    date('h:i A', strtotime($activeTimeRange['afternoon_time_in_end'])) . "</li>";

                                echo "<li><strong>Afternoon Time Out:</strong> " .
                                    date('h:i A', strtotime($activeTimeRange['afternoon_time_out_start'])) . " - " .
                                    date('h:i A', strtotime($activeTimeRange['afternoon_time_out_end'])) . "</li>";
                            } else {
                                // Default values if no active time range is found
                                echo "<li><strong>Morning Time In:</strong> 7:00 AM - 8:30 AM</li>";
                                echo "<li><strong>Morning Time Out:</strong> 12:00 PM - 12:30 PM</li>";
                                echo "<li><strong>Afternoon Time In:</strong> 12:31 PM - 1:30 PM</li>";
                                echo "<li><strong>Afternoon Time Out:</strong> 5:00 PM - 6:00 PM</li>";
                            }
                        } catch (PDOException $e) {
                            // Default values if there's an error
                            echo "<li><strong>Morning Time In:</strong> 7:00 AM - 8:30 AM</li>";
                            echo "<li><strong>Morning Time Out:</strong> 12:00 PM - 12:30 PM</li>";
                            echo "<li><strong>Afternoon Time In:</strong> 12:31 PM - 1:30 PM</li>";
                            echo "<li><strong>Afternoon Time Out:</strong> 5:00 PM - 6:00 PM</li>";
                        }
                        ?>
                        <li class="mt-2"><small><em>Note: All time ranges include a ±30 minute grace period</em></small></li>
                    </ul>
                </div>

                <div style="text-align: center ">
                    <div id="reader"></div>
                    <div id="result"></div>
                </div>
                <!-- Add a hidden form token to prevent duplicate submissions -->
                <?php
                // Generate a unique token and store it in the session
                if (!isset($_SESSION['form_token'])) {
                    $_SESSION['form_token'] = bin2hex(random_bytes(32));
                }
                ?>
                <input type="hidden" name="form_token" value="<?php echo $_SESSION['form_token']; ?>">
                <button type="submit" class="btn btn-primary mt-3" id="submitBtn">Submit</button>
            </form>
        </section>
    </div>

    <!-- Footer with Date and Time -->
    <footer class="text-center p-2" id="footer">
        <div class="footer-date-time" id="dateTime">Loading...</div>
        © 2025 Developed by:
        <a class="text-dark" href="#">John Lloyd Caban</a>
    </footer>

    <!-- Footer -->

    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script type="text/javascript" src="../assets/MDB5-STANDARD-UI-KIT/js/mdb.umd.min.js"></script>

    <script>
        // Wait for the DOM to be fully loaded before executing any JavaScript
        document.addEventListener("DOMContentLoaded", function() {
            // Prevent form resubmission when page is reloaded
            if (window.history.replaceState) {
                window.history.replaceState(null, null, window.location.href);
            }

            // Enhanced validation functions
            const ValidationUtils = {
                // Employee ID validation
                validateEmployeeId: function(employeeId) {
                    const errors = [];

                    if (!employeeId || employeeId.trim() === '') {
                        errors.push('Employee ID is required');
                        return { isValid: false, errors };
                    }

                    const trimmedId = employeeId.trim();

                    // Length validation
                    if (trimmedId.length < 3) {
                        errors.push('Employee ID must be at least 3 characters long');
                    }
                    if (trimmedId.length > 20) {
                        errors.push('Employee ID cannot exceed 20 characters');
                    }

                    // Format validation
                    const validPattern = /^[A-Za-z0-9\-_]+$/;
                    if (!validPattern.test(trimmedId)) {
                        errors.push('Employee ID can only contain letters, numbers, hyphens, and underscores');
                    }

                    // SQL injection prevention
                    const sqlPatterns = [
                        /('|(\\')|(;)|(\\;)|(select)|(insert)|(update)|(delete)|(drop)|(create)|(alter)|(exec)|(execute)|(union)|(script)/gi
                    ];

                    for (let pattern of sqlPatterns) {
                        if (pattern.test(trimmedId)) {
                            errors.push('Invalid characters detected in Employee ID');
                            break;
                        }
                    }

                    return {
                        isValid: errors.length === 0,
                        errors: errors,
                        cleanValue: trimmedId
                    };
                },

                // Real-time input validation
                setupRealTimeValidation: function() {
                    const employeeIdInput = document.getElementById('employeeId');
                    const feedbackDiv = document.getElementById('employeeIdFeedback');

                    if (!employeeIdInput || !feedbackDiv) return;

                    let validationTimeout;

                    employeeIdInput.addEventListener('input', function() {
                        clearTimeout(validationTimeout);

                        validationTimeout = setTimeout(() => {
                            const validation = ValidationUtils.validateEmployeeId(this.value);

                            // Remove previous validation classes
                            this.classList.remove('is-valid', 'is-invalid');
                            feedbackDiv.style.display = 'none';

                            if (this.value.trim() === '') {
                                // Empty field - neutral state
                                return;
                            }

                            if (validation.isValid) {
                                this.classList.add('is-valid');
                                feedbackDiv.className = 'valid-feedback';
                                feedbackDiv.textContent = '✓ Valid Employee ID format';
                                feedbackDiv.style.display = 'block';
                            } else {
                                this.classList.add('is-invalid');
                                feedbackDiv.className = 'invalid-feedback';
                                feedbackDiv.textContent = validation.errors[0];
                                feedbackDiv.style.display = 'block';
                            }
                        }, 300); // Debounce validation
                    });

                    // Prevent paste of invalid content
                    employeeIdInput.addEventListener('paste', function(e) {
                        setTimeout(() => {
                            const validation = ValidationUtils.validateEmployeeId(this.value);
                            if (!validation.isValid) {
                                this.value = validation.cleanValue || '';
                                ValidationUtils.showToast('warning', 'Invalid content removed from Employee ID');
                            }
                        }, 10);
                    });
                },

                // Enhanced toast notifications
                showToast: function(icon, title, text = '', position = 'top-end') {
                    const Toast = Swal.mixin({
                        toast: true,
                        position: position,
                        showConfirmButton: false,
                        timer: 3000,
                        timerProgressBar: true,
                        didOpen: (toast) => {
                            toast.addEventListener('mouseenter', Swal.stopTimer);
                            toast.addEventListener('mouseleave', Swal.resumeTimer);
                        }
                    });

                    return Toast.fire({
                        icon: icon,
                        title: title,
                        text: text
                    });
                },

                // Rate limiting for form submissions
                rateLimiter: {
                    lastSubmission: 0,
                    minInterval: 2000, // 2 seconds between submissions

                    canSubmit: function() {
                        const now = Date.now();
                        if (now - this.lastSubmission < this.minInterval) {
                            const remainingTime = Math.ceil((this.minInterval - (now - this.lastSubmission)) / 1000);
                            ValidationUtils.showToast('warning', 'Please wait', `Try again in ${remainingTime} second${remainingTime > 1 ? 's' : ''}`);
                            return false;
                        }
                        this.lastSubmission = now;
                        return true;
                    }
                }
            };

            // Initialize real-time validation
            ValidationUtils.setupRealTimeValidation();

            // Function to check if today is a holiday
            function checkIfHoliday() {
                return new Promise((resolve, reject) => {
                    const today = new Date().toISOString().split('T')[0]; // Format: YYYY-MM-DD
                    fetch('check_holiday.php?date=' + today)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success && data.is_holiday) {
                                resolve(data.holiday);
                            } else {
                                resolve(false);
                            }
                        })
                        .catch(error => {
                            console.error('Error checking holiday:', error);
                            resolve(false); // Resolve with false on error to allow form submission
                        });
                });
            }

            // Add event listener to the form to handle submission
            const form = document.getElementById('attendanceForm');
            if (form) {
                form.addEventListener('submit', function(event) {
                    const submitBtn = document.getElementById('submitBtn');
                    const employeeIdInput = document.getElementById('employeeId');

                    // Prevent form submission temporarily for all cases
                    event.preventDefault();

                    // Rate limiting check
                    if (!ValidationUtils.rateLimiter.canSubmit()) {
                        return;
                    }

                    // Client-side validation
                    const validation = ValidationUtils.validateEmployeeId(employeeIdInput.value);
                    if (!validation.isValid) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Invalid Employee ID',
                            html: `
                                <div class="text-center">
                                    <i class="fas fa-exclamation-triangle fa-2x mb-3 text-warning"></i>
                                    <ul class="text-left" style="list-style: none; padding: 0;">
                                        ${validation.errors.map(error => `<li><i class="fas fa-times text-danger me-2"></i>${error}</li>`).join('')}
                                    </ul>
                                </div>
                            `,
                            confirmButtonColor: '#d33',
                            confirmButtonText: 'Fix Issues',
                            didOpen: () => {
                                employeeIdInput.focus();
                                employeeIdInput.classList.add('is-invalid');
                            }
                        });
                        return;
                    }

                    // Show enhanced loading state
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.classList.add('btn-loading');
                        submitBtn.innerHTML = '<span style="opacity: 0;">Processing...</span>';
                    }

                    // Show processing toast
                    ValidationUtils.showToast('info', 'Validating...', 'Checking employee information', 'center');

                    // First check if today is a holiday
                    checkIfHoliday().then(holiday => {
                        if (holiday) {
                            // It's a holiday
                            Swal.fire({
                                icon: 'info',
                                title: 'Non-Working Day Detected',
                                html: `
                                    <div class="text-center">
                                        <i class="fas fa-calendar-times fa-3x mb-3 text-info"></i>
                                        <h5 class="mb-3">Today is a Holiday</h5>
                                        <div class="alert alert-info" role="alert">
                                            <strong>${holiday.holiday_name}</strong>
                                        </div>
                                        <p class="text-muted">
                                            <i class="fas fa-info-circle me-2"></i>
                                            In accordance with company policy, attendance records cannot be created for official holidays.
                                        </p>
                                        <small class="text-muted">Enjoy your day off! 🎉</small>
                                    </div>
                                `,
                                confirmButtonColor: '#17a2b8',
                                confirmButtonText: 'Understood',
                                allowOutsideClick: false,
                                customClass: {
                                    popup: 'swal2-holiday-popup'
                                }
                            }).then(() => {
                                // Re-enable the submit button
                                if (submitBtn) {
                                    submitBtn.disabled = false;
                                    submitBtn.classList.remove('btn-loading');
                                    submitBtn.innerHTML = 'Submit';
                                }
                            });
                            return; // Stop form submission
                        }

                        // If it's not a holiday, proceed with employee status check
                        if (employeeIdInput.value.trim() !== '') {
                            fetch('check_employee_status.php?employeeId=' + encodeURIComponent(validation.cleanValue))
                                .then(response => {
                                    if (!response.ok) {
                                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                                    }
                                    return response.json();
                                })
                                .then(data => {
                                    if (data.error) {
                                        // Employee not found
                                        Swal.fire({
                                            icon: 'error',
                                            title: 'Employee Not Found',
                                            html: `
                                                <div class="text-center">
                                                    <i class="fas fa-user-slash fa-3x mb-3 text-danger"></i>
                                                    <h5 class="mb-3">No Employee Record Found</h5>
                                                    <div class="alert alert-danger" role="alert">
                                                        Employee ID: <strong>${validation.cleanValue}</strong>
                                                    </div>
                                                    <p class="text-muted">
                                                        <i class="fas fa-search me-2"></i>
                                                        Please verify the Employee ID and try again.
                                                    </p>
                                                    <small class="text-muted">Contact HR if you believe this is an error.</small>
                                                </div>
                                            `,
                                            confirmButtonColor: '#d33',
                                            confirmButtonText: 'Try Again',
                                            didClose: () => {
                                                employeeIdInput.focus();
                                                employeeIdInput.select();
                                            }
                                        });
                                    } else if (data.is_absent) {
                                        // Employee is already marked as absent
                                        Swal.fire({
                                            icon: 'warning',
                                            title: 'Already Marked Absent',
                                            html: `
                                                <div class="text-center">
                                                    <i class="fas fa-calendar-times fa-3x mb-3 text-warning"></i>
                                                    <h5 class="mb-3">Cannot Record Attendance</h5>
                                                    <div class="alert alert-warning" role="alert">
                                                        <strong>${data.employee_name}</strong> is already marked as absent for today.
                                                    </div>
                                                    <p class="text-muted">
                                                        <i class="fas fa-ban me-2"></i>
                                                        Attendance cannot be recorded once marked as absent.
                                                    </p>
                                                    <small class="text-muted">Contact your supervisor if this is incorrect.</small>
                                                </div>
                                            `,
                                            confirmButtonColor: '#ffc107',
                                            confirmButtonText: 'Understood'
                                        });
                                    } else if (data.hours_completed) {
                                        // Employee has completed required hours
                                        Swal.fire({
                                            icon: 'success',
                                            title: 'Congratulations!',
                                            html: `
                                                <div class="text-center">
                                                    <i class="fas fa-trophy fa-3x mb-3 text-success"></i>
                                                    <h5 class="mb-3">Hours Requirement Fulfilled</h5>
                                                    <div class="alert alert-success" role="alert">
                                                        <strong>${data.employee_name}</strong> has completed all required hours!
                                                    </div>
                                                    <div class="row text-center mt-3">
                                                        <div class="col-6">
                                                            <div class="border-end">
                                                                <h6 class="text-muted">Required</h6>
                                                                <h4 class="text-primary">${data.required_hours || 'N/A'}</h4>
                                                                <small>hours</small>
                                                            </div>
                                                        </div>
                                                        <div class="col-6">
                                                            <h6 class="text-muted">Completed</h6>
                                                            <h4 class="text-success">${data.total_hours || 'N/A'}</h4>
                                                            <small>hours</small>
                                                        </div>
                                                    </div>
                                                    <p class="text-muted mt-3">
                                                        <i class="fas fa-check-circle me-2"></i>
                                                        No further attendance recording needed.
                                                    </p>
                                                </div>
                                            `,
                                            confirmButtonColor: '#28a745',
                                            confirmButtonText: 'Excellent!',
                                            showClass: {
                                                popup: 'animate__animated animate__bounceIn'
                                            }
                                        });
                                    } else {
                                        // Employee can record attendance, proceed with form submission
                                        ValidationUtils.showToast('success', 'Validation Complete', 'Processing attendance...');
                                        setTimeout(() => {
                                            form.submit();
                                        }, 500);
                                    }
                                })
                                .catch(error => {
                                    console.error('Error checking employee status:', error);

                                    // Enhanced error handling with different error types
                                    let errorTitle = 'System Error';
                                    let errorMessage = 'An unexpected error occurred while validating employee information.';
                                    let errorIcon = 'error';

                                    if (error.message.includes('HTTP 404')) {
                                        errorTitle = 'Service Unavailable';
                                        errorMessage = 'Employee validation service is temporarily unavailable.';
                                    } else if (error.message.includes('HTTP 500')) {
                                        errorTitle = 'Server Error';
                                        errorMessage = 'Internal server error occurred. Please try again later.';
                                    } else if (error.message.includes('Failed to fetch')) {
                                        errorTitle = 'Connection Error';
                                        errorMessage = 'Unable to connect to the server. Please check your internet connection.';
                                        errorIcon = 'warning';
                                    }

                                    Swal.fire({
                                        icon: errorIcon,
                                        title: errorTitle,
                                        html: `
                                            <div class="text-center">
                                                <i class="fas fa-exclamation-triangle fa-3x mb-3 text-danger"></i>
                                                <h5 class="mb-3">${errorTitle}</h5>
                                                <div class="alert alert-danger" role="alert">
                                                    ${errorMessage}
                                                </div>
                                                <p class="text-muted">
                                                    <i class="fas fa-tools me-2"></i>
                                                    Please try again or contact technical support if the problem persists.
                                                </p>
                                                <details class="mt-3">
                                                    <summary class="text-muted" style="cursor: pointer;">Technical Details</summary>
                                                    <small class="text-muted">${error.message}</small>
                                                </details>
                                            </div>
                                        `,
                                        confirmButtonColor: '#d33',
                                        confirmButtonText: 'Close',
                                        footer: '<small>Error Code: ' + Date.now() + '</small>'
                                    });
                                })
                                .finally(() => {
                                    // Always re-enable the submit button
                                    if (submitBtn) {
                                        submitBtn.disabled = false;
                                        submitBtn.classList.remove('btn-loading');
                                        submitBtn.innerHTML = 'Submit';
                                    }
                                });
                        } else {
                            // This should never happen due to client-side validation, but just in case
                            ValidationUtils.showToast('error', 'Employee ID Required', 'Please enter a valid Employee ID');
                            if (submitBtn) {
                                submitBtn.disabled = false;
                                submitBtn.classList.remove('btn-loading');
                                submitBtn.innerHTML = 'Submit';
                            }
                        }
                    });
                });
            }

            // Function to update the date and time in real-time
            function updateDateTime() {
                const now = new Date();

                const options = {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                    hour: "2-digit",
                    minute: "2-digit",
                    second: "2-digit",
                    hour12: true
                };

                const dateTimeElement = document.getElementById("dateTime");
                if (dateTimeElement) {
                    dateTimeElement.innerText = now.toLocaleString("en-US", options);
                }
            }

            // Start updating the date and time immediately
            updateDateTime();

            // Update the date and time every 1 second
            const dateTimeInterval = setInterval(updateDateTime, 1000);

            // Initialize QR code scanner
            const scanner = new Html5QrcodeScanner("reader", {
                qrbox: {
                    width: 250,
                    height: 250,
                },
                fps: 20,
            });

            scanner.render(success, error);

            function success(result) {
                console.log("Barcode scanned successfully:", result);
                document.getElementById("result").innerHTML = `
                    <h2>Success!</h2>
                `;
                document.getElementById("employeeId").value = result; // Set scanned value to input field
                scanner.clear();
                document.getElementById("reader").remove();

                // Show scanning feedback
                Swal.fire({
                    icon: 'info',
                    title: 'Processing...',
                    text: 'Recording attendance for ID: ' + result,
                    showConfirmButton: false,
                    allowOutsideClick: false,
                    willOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Disable the submit button to prevent multiple submissions
                const submitBtn = document.getElementById("submitBtn");
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = 'Processing...';
                }

                // Check if today is a holiday before submitting
                checkIfHoliday().then(holiday => {
                    if (holiday) {
                        // It's a holiday
                        Swal.fire({
                            icon: 'error',
                            title: 'Non-Working Day Detected',
                            html: `
                                <p>Today is a recognized holiday:</p>
                                <p><strong>${holiday.holiday_name}</strong></p>
                                <p>In accordance with company policy, attendance records cannot be created for official holidays.</p>
                            `
                        }).then(() => {
                            // Re-enable the submit button
                            if (submitBtn) {
                                submitBtn.disabled = false;
                                submitBtn.innerHTML = 'Submit';
                            }
                        });
                    } else {
                        // Not a holiday, proceed with employee status check
                        fetch('check_employee_status.php?employeeId=' + encodeURIComponent(result))
                            .then(response => response.json())
                            .then(data => {
                                if (data.error) {
                                    // Employee not found or other error
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Employee ID Error',
                                        html: `<div class="text-center">
                                            <i class="fas fa-user-slash fa-3x mb-3 text-danger"></i>
                                            <p>No employee found with ID: <strong>${result}</strong></p>
                                            <p class="text-muted small">Please verify the ID and try again.</p>
                                        </div>`,
                                        confirmButtonColor: '#d33',
                                        confirmButtonText: 'Try Again'
                                    }).then(() => {
                                        // Re-enable the submit button
                                        if (submitBtn) {
                                            submitBtn.disabled = false;
                                            submitBtn.innerHTML = 'Submit';
                                        }
                                    });
                                } else if (!data.is_active) {
                                    // Employee account is inactive
                                    Swal.fire({
                                        icon: 'warning',
                                        title: 'Inactive Account',
                                        html: `<div class="text-center">
                                            <i class="fas fa-user-lock fa-3x mb-3 text-warning"></i>
                                            <p>The account for <strong>${data.employee_name}</strong> is currently inactive.</p>
                                            <p class="text-muted small">Please contact your administrator to reactivate your account.</p>
                                        </div>`,
                                        confirmButtonColor: '#3085d6',
                                        confirmButtonText: 'OK'
                                    }).then(() => {
                                        if (submitBtn) {
                                            submitBtn.disabled = false;
                                            submitBtn.innerHTML = 'Submit';
                                        }
                                    });
                                } else if (data.is_absent) {
                                    // Employee is already marked as absent
                                    Swal.fire({
                                        icon: 'warning',
                                        title: 'Cannot Record Attendance',
                                        html: `<div class="text-center">
                                            <i class="fas fa-calendar-times fa-3x mb-3 text-warning"></i>
                                            <p><strong>${data.employee_name}</strong> is already marked as absent for today.</p>
                                            <p class="text-muted small">Attendance cannot be recorded once marked as absent.</p>
                                        </div>`,
                                        confirmButtonColor: '#3085d6',
                                        confirmButtonText: 'Understood'
                                    }).then(() => {
                                        if (submitBtn) {
                                            submitBtn.disabled = false;
                                            submitBtn.innerHTML = 'Submit';
                                        }
                                    });
                                } else if (data.hours_completed) {
                                    // Employee has completed required hours
                                    Swal.fire({
                                        icon: 'info',
                                        title: 'Hours Requirement Fulfilled',
                                        html: `<div class="text-center">
                                            <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                                            <p><strong>${data.employee_name}</strong> has completed all required hours.</p>
                                            <p class="text-muted small">Required: ${data.required_hours} hours | Completed: ${data.total_hours} hours</p>
                                        </div>`,
                                        confirmButtonColor: '#28a745',
                                        confirmButtonText: 'Great!'
                                    }).then(() => {
                                        if (submitBtn) {
                                            submitBtn.disabled = false;
                                            submitBtn.innerHTML = 'Submit';
                                        }
                                    });
                                } else if (data.outside_location) {
                                    // Employee trying to log attendance from unauthorized location
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Unauthorized Location',
                                        html: `<div class="text-center">
                                            <i class="fas fa-map-marker-alt fa-3x mb-3 text-danger"></i>
                                            <p>You must be at your assigned location to record attendance.</p>
                                            <p class="text-muted small">Your assigned location: ${data.assigned_location}</p>
                                        </div>`,
                                        confirmButtonColor: '#d33',
                                        confirmButtonText: 'OK'
                                    }).then(() => {
                                        if (submitBtn) {
                                            submitBtn.disabled = false;
                                            submitBtn.innerHTML = 'Submit';
                                        }
                                    });
                                } else {
                                    console.log("Submitting form after barcode scan");
                                    // Employee can record attendance, proceed with form submission
                                    document.querySelector(".glassmorphism-form").submit();
                                }
                            })
                            .catch(error => {
                                console.error('Error checking employee status:', error);
                                // Show error message instead of proceeding with form submission
                                Swal.fire({
                                    icon: 'error',
                                    title: 'System Error',
                                    html: `<div class="text-center">
                                        <i class="fas fa-exclamation-triangle fa-3x mb-3 text-danger"></i>
                                        <p>Failed to verify employee information.</p>
                                        <p class="text-muted small">Please try again or contact technical support.</p>
                                    </div>`,
                                    confirmButtonColor: '#d33',
                                    confirmButtonText: 'Close'
                                }).then(() => {
                                    if (submitBtn) {
                                        submitBtn.disabled = false;
                                        submitBtn.innerHTML = 'Submit';
                                    }
                                });
                            });
                    }
                });
            }

            function error(err) {
                console.error("QR Scanner error:", err);
            }

            <?php if (isset($_SESSION['attendance_result']) && isset($_SESSION['attendance_result']['processed'])): ?>
                <?php
                // Check if the result is recent (within the last 5 seconds)
                $isRecent = (time() - $_SESSION['attendance_result']['timestamp']) < 5;

                if ($isRecent):
                    $status = $_SESSION['attendance_result']['status'] ?? 'info';
                    $message = $_SESSION['attendance_result']['message'] ?? 'Attendance';
                    $details = $_SESSION['attendance_result']['details'] ?? 'Your attendance has been processed.';
                ?>
                    console.log("Showing attendance result:", <?php echo json_encode($_SESSION['attendance_result']); ?>);
                    Swal.fire({
                        icon: '<?php echo $status; ?>',
                        title: '<?php echo $message; ?>',
                        text: '<?php echo addslashes($details); ?>',
                        confirmButtonText: 'OK',
                        timer: 5000,
                        didOpen: () => {
                            const footer = document.getElementById('footer');
                            const form = document.querySelector('.glassmorphism-form');
                            if (footer) {
                                console.log("Hiding footer");
                                footer.classList.add('hidden');
                            } else {
                                console.error("Footer element not found");
                            }
                            if (form) {
                                console.log("Hiding form");
                                form.classList.add('hidden');
                            } else {
                                console.error("Form element not found");
                            }
                        }
                    }).then(function() {
                        const footer = document.getElementById('footer');
                        const form = document.querySelector('.glassmorphism-form');
                        if (footer) {
                            console.log("Showing footer");
                            footer.classList.remove('hidden');
                        }
                        if (form) {
                            console.log("Showing form");
                            form.classList.remove('hidden');

                            // Reset the form after showing result
                            form.reset();

                            // Reload the scanner if needed
                            if (document.getElementById("reader") === null) {
                                const readerDiv = document.createElement('div');
                                readerDiv.id = 'reader';
                                document.querySelector('#result').innerHTML = '';
                                document.querySelector('#result').parentNode.insertBefore(readerDiv, document.querySelector('#result'));

                                // Re-initialize scanner
                                const scanner = new Html5QrcodeScanner("reader", {
                                    qrbox: {
                                        width: 250,
                                        height: 250,
                                    },
                                    fps: 20,
                                });
                                scanner.render(success, error);
                            }
                        }
                    });
                <?php
                    // Clear the result after displaying it
                    unset($_SESSION['attendance_result']);
                endif;
                ?>
            <?php endif; ?>

        });
    </script>

</body>

</html>