<?php
header('Content-Type: application/json');

// Set default timezone
date_default_timezone_set('Asia/Manila');

try {
    $connect = new PDO("mysql:host=localhost;dbname=attendance_monitoring_db", "root", "");
    $connect->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => true, 'message' => 'Database connection failed: ' . $e->getMessage()]);
    exit;
}

if (!isset($_GET['employeeId']) || empty($_GET['employeeId'])) {
    echo json_encode(['error' => true, 'message' => 'Employee ID is required']);
    exit;
}

$employeeId = trim($_GET['employeeId']);

// Enhanced input validation
if (strlen($employeeId) < 3 || strlen($employeeId) > 20) {
    echo json_encode(['error' => true, 'message' => 'Employee ID must be between 3 and 20 characters']);
    exit;
}

// Check for valid characters only
if (!preg_match('/^[A-Za-z0-9\-_]+$/', $employeeId)) {
    echo json_encode(['error' => true, 'message' => 'Employee ID contains invalid characters']);
    exit;
}

// SQL injection prevention
$sqlPatterns = [
    '/(\bselect\b|\binsert\b|\bupdate\b|\bdelete\b|\bdrop\b|\bcreate\b|\balter\b|\bexec\b|\bunion\b)/i',
    '/[\'";\\\\]/',
    '/--/',
    '/\/\*/',
    '/\*\//'
];

foreach ($sqlPatterns as $pattern) {
    if (preg_match($pattern, $employeeId)) {
        echo json_encode(['error' => true, 'message' => 'Invalid Employee ID format']);
        exit;
    }
}

// Fetch employee details
$stmt = $connect->prepare("SELECT * FROM tbl_employee WHERE employee_id = :employeeId");
$stmt->bindParam(':employeeId', $employeeId);
$stmt->execute();
$employee = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$employee) {
    // Employee not found
    echo json_encode(['error' => true, 'message' => 'No employee found with this ID']);
    exit;
}

$tblEmpId = $employee['tbl_emp_id'];
$employeeName = $employee['first_name'] . ' ' . ($employee['middle_initial'] ? $employee['middle_initial'] . ' ' : '') . $employee['surname'];

// Check if hours are completed and get detailed information
$hoursCompleted = false;
$requiredHours = 0;
$totalHours = 0;
$isActive = true;

try {
    // Get the employee's required hours and status
    $empStmt = $connect->prepare("SELECT no_hours_required, is_active FROM tbl_employee WHERE tbl_emp_id = :empId");
    $empStmt->bindParam(":empId", $tblEmpId);
    $empStmt->execute();
    $empDetails = $empStmt->fetch(PDO::FETCH_ASSOC);

    if ($empDetails) {
        $requiredHours = $empDetails['no_hours_required'] ?? 0;
        $isActive = $empDetails['is_active'] == 1;

        // Get total hours logged by employee
        $hoursStmt = $connect->prepare("SELECT SUM(total_daily_hours) as total_hours FROM tbl_attendance WHERE tbl_emp_id = :empId");
        $hoursStmt->bindParam(":empId", $tblEmpId);
        $hoursStmt->execute();
        $result = $hoursStmt->fetch(PDO::FETCH_ASSOC);

        $totalHours = $result['total_hours'] ?? 0;

        // Check if total hours meets or exceeds required hours
        $hoursCompleted = $totalHours >= $requiredHours;
    }
} catch (PDOException $e) {
    error_log("Error checking completed hours: " . $e->getMessage());
}

// Check if already absent for today
$isAbsent = false;
try {
    $checkStmt = $connect->prepare("SELECT * FROM tbl_attendance WHERE tbl_emp_id = :employeeId AND DATE(attendance_date) = CURDATE() AND is_absent = 1");
    $checkStmt->bindParam(':employeeId', $tblEmpId);
    $checkStmt->execute();
    $attendanceRecord = $checkStmt->fetch(PDO::FETCH_ASSOC);

    $isAbsent = ($attendanceRecord !== false);
} catch (PDOException $e) {
    error_log("Error checking absence status: " . $e->getMessage());
}

// Return enhanced employee status
echo json_encode([
    'error' => false,
    'found' => true,
    'employee_id' => $employeeId,
    'tbl_emp_id' => $tblEmpId,
    'employee_name' => $employeeName,
    'hours_completed' => $hoursCompleted,
    'is_absent' => $isAbsent,
    'is_active' => $isActive,
    'required_hours' => $requiredHours,
    'total_hours' => round($totalHours, 2),
    'remaining_hours' => max(0, round($requiredHours - $totalHours, 2)),
    'completion_percentage' => $requiredHours > 0 ? round(($totalHours / $requiredHours) * 100, 1) : 0,
    'validation_timestamp' => date('Y-m-d H:i:s'),
    'server_time' => date('h:i:s A')
]);
exit;
?>
