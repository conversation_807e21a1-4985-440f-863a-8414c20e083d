<?php
header('Content-Type: application/json');

// Set default timezone
date_default_timezone_set('Asia/Manila');

try {
    $connect = new PDO("mysql:host=localhost;dbname=attendance_monitoring_db", "root", "");
    $connect->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => true, 'message' => 'Database connection failed: ' . $e->getMessage()]);
    exit;
}

if (!isset($_GET['employeeId']) || empty($_GET['employeeId'])) {
    echo json_encode(['error' => true, 'message' => 'Employee ID is required']);
    exit;
}

$employeeId = $_GET['employeeId'];

// Fetch employee details
$stmt = $connect->prepare("SELECT * FROM tbl_employee WHERE employee_id = :employeeId");
$stmt->bindParam(':employeeId', $employeeId);
$stmt->execute();
$employee = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$employee) {
    // Employee not found
    echo json_encode(['error' => true, 'message' => 'No employee found with this ID']);
    exit;
}

$tblEmpId = $employee['tbl_emp_id'];
$employeeName = $employee['first_name'] . ' ' . ($employee['middle_initial'] ? $employee['middle_initial'] . ' ' : '') . $employee['surname'];

// Check if hours are completed
$hoursCompleted = false;
try {
    // Get the employee's required hours
    $empStmt = $connect->prepare("SELECT no_hours_required FROM tbl_employee WHERE tbl_emp_id = :empId");
    $empStmt->bindParam(":empId", $tblEmpId);
    $empStmt->execute();
    $empDetails = $empStmt->fetch(PDO::FETCH_ASSOC);
    
    if ($empDetails && isset($empDetails['no_hours_required'])) {
        $requiredHours = $empDetails['no_hours_required'];
        
        // Get total hours logged by employee
        $hoursStmt = $connect->prepare("SELECT SUM(total_daily_hours) as total_hours FROM tbl_attendance WHERE tbl_emp_id = :empId");
        $hoursStmt->bindParam(":empId", $tblEmpId);
        $hoursStmt->execute();
        $result = $hoursStmt->fetch(PDO::FETCH_ASSOC);
        
        $totalHours = $result['total_hours'] ?? 0;
        
        // Check if total hours meets or exceeds required hours
        $hoursCompleted = $totalHours >= $requiredHours;
    }
} catch (PDOException $e) {
    error_log("Error checking completed hours: " . $e->getMessage());
}

// Check if already absent for today
$isAbsent = false;
try {
    $checkStmt = $connect->prepare("SELECT * FROM tbl_attendance WHERE tbl_emp_id = :employeeId AND DATE(attendance_date) = CURDATE() AND is_absent = 1");
    $checkStmt->bindParam(':employeeId', $tblEmpId);
    $checkStmt->execute();
    $attendanceRecord = $checkStmt->fetch(PDO::FETCH_ASSOC);
    
    $isAbsent = ($attendanceRecord !== false);
} catch (PDOException $e) {
    error_log("Error checking absence status: " . $e->getMessage());
}

// Return employee status
echo json_encode([
    'error' => false,
    'found' => true,
    'employee_id' => $employeeId,
    'tbl_emp_id' => $tblEmpId,
    'employee_name' => $employeeName,
    'hours_completed' => $hoursCompleted,
    'is_absent' => $isAbsent
]);
exit;
?>
